# منصة تدبير الحوادث المدرسية - Daman School

## نظرة عامة

منصة تدبير الحوادث المدرسية هي تطبيق سطح مكتب مطور بلغة Python لإدارة وتتبع الحوادث المدرسية في المؤسسات التعليمية. يوفر التطبيق واجهة عربية حديثة وسهلة الاستخدام مع دعم كامل للغة العربية (RTL).

## المميزات الرئيسية

### 🏠 لوحة التحكم الرئيسية
- عرض إحصائيات شاملة للحوادث
- آخر الحوادث المسجلة
- إجراءات سريعة للوصول للميزات الأساسية

### 📝 إدارة الحوادث
- إضافة حوادث جديدة مع جميع التفاصيل المطلوبة
- تصفية وبحث متقدم في الحوادث
- عرض تفاصيل كاملة لكل حادثة
- تعديل وحذف الحوادث
- تصدير البيانات

### 🏫 إدارة المؤسسات التعليمية
- إضافة وإدارة المؤسسات التعليمية
- تصنيف حسب السلك والوسط
- ربط المؤسسات بالحوادث
- إحصائيات لكل مؤسسة

### 📊 التقارير والإحصائيات
- تقارير شاملة ومفصلة
- إحصائيات حسب السنة الدراسية
- تحليل البيانات بصرياً
- تصدير التقارير بصيغ مختلفة

### ⚙️ الإعدادات المتقدمة
- إعدادات العرض (حجم الخط، المظهر)
- النسخ الاحتياطية واستعادة البيانات
- استيراد البيانات من ملفات Excel
- إدارة السنوات الدراسية
- إدارة المستخدمين

## المتطلبات التقنية

### متطلبات النظام
- Windows 7 أو أحدث
- Python 3.8 أو أحدث
- ذاكرة RAM: 4 GB أو أكثر
- مساحة تخزين: 500 MB أو أكثر

### المكتبات المطلوبة
```
Flask>=2.3.3
pywebview>=4.4.1
openpyxl>=3.1.2
python-dateutil>=2.8.2
Werkzeug>=2.3.7
Jinja2>=3.1.2
pyinstaller>=6.10.0
```

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd "Daman school"
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق

#### وضع التطوير
```bash
python main.py --debug
```

#### وضع الإنتاج
```bash
python main.py
```

### 4. إنشاء ملف تنفيذي
```bash
pyinstaller --onefile --windowed main.py
```

## هيكل المشروع

```
Daman_School/
├── main.py                    # نقطة البداية الرئيسية
├── requirements.txt           # المتطلبات
├── README.md                  # توثيق المشروع
├── config/                    # إعدادات التطبيق
│   ├── __init__.py
│   ├── database.py           # إعدادات قاعدة البيانات
│   └── settings.py           # إعدادات التطبيق
├── models/                    # نماذج البيانات
│   └── __init__.py
├── database/                  # قاعدة البيانات
│   ├── __init__.py
│   └── daman_school.db       # ملف قاعدة البيانات SQLite
├── api/                       # واجهات برمجة التطبيقات
│   ├── __init__.py
│   ├── incidents.py          # API للحوادث
│   ├── institutions.py       # API للمؤسسات
│   └── reports.py            # API للتقارير
├── web/                       # الواجهة الأمامية
│   ├── static/
│   │   ├── css/
│   │   │   └── style.css     # التصميم الرئيسي
│   │   └── js/
│   │       ├── main.js       # JavaScript الرئيسي
│   │       └── api.js        # تفاعل مع API
│   └── templates/
│       ├── index.html        # الصفحة الرئيسية
│       ├── add_incident.html # إضافة حادثة
│       ├── incidents.html    # قائمة الحوادث
│       ├── institutions.html # المؤسسات
│       └── settings.html     # الإعدادات
└── utils/                     # أدوات مساعدة
    ├── __init__.py
    ├── excel_import.py       # استيراد من Excel
    └── backup.py             # النسخ الاحتياطية
```

## قاعدة البيانات

يستخدم التطبيق قاعدة بيانات SQLite مع الجداول التالية:

### جدول الحوادث (incidents)
- معلومات المتضرر (الاسم، النوع، رقم مسار)
- معلومات المؤسسة (الاسم، الرمز، السلك، الوسط)
- تفاصيل الحادثة (التاريخ، النوع، الخطورة، الحالة)

### جدول المؤسسات (institutions)
- المعلومات الأساسية (الرمز، الاسم، الجماعة)
- التصنيف (السلك، الوسط، الطبيعة)
- معلومات الاتصال (المدير، الهاتف، البريد)

### جدول المستفيدين (beneficiaries)
- معلومات المستفيدين من التعويض
- العلاقة بالمتضرر (الأب، الأم، الولي)

### جدول المستخدمين (users)
- إدارة مستخدمي النظام
- الأدوار والصلاحيات

## الاستخدام

### إضافة حادثة جديدة
1. انقر على "إضافة حادثة" من القائمة الجانبية
2. املأ جميع البيانات المطلوبة
3. أضف المستفيدين من التعويض
4. احفظ الحادثة

### البحث والتصفية
- استخدم مربع البحث للبحث السريع
- طبق المرشحات حسب النوع، السلك، الحالة، إلخ
- حدد نطاق تاريخي للبحث

### تصدير البيانات
- اختر المرشحات المطلوبة
- انقر على "تصدير النتائج"
- احفظ الملف بالصيغة المطلوبة

### استيراد من Excel
1. اذهب إلى الإعدادات
2. اختر ملف Excel
3. حدد السنة الدراسية
4. انقر على "استيراد البيانات"

## تنسيق ملف Excel للاستيراد

يجب أن يحتوي ملف Excel على الأعمدة التالية:

1. اسم التلميذ المؤمن
2. النوع (ذكر/أنثى)
3. المؤسسة
4. السلك (ابتدائي/إعدادي/تأهيلي)
5. الوسط (قروي/حضري)
6. المرجع
7. تاريخ وقوع الحادثة
8. تاريخ التسوية
9. الحالة (في طور التسوية/تمت التسوية/مرفوض/تم الدفع)
10. رقم الملف

## النسخ الاحتياطية

### إنشاء نسخة احتياطية
- اذهب إلى الإعدادات > النسخ الاحتياطية
- انقر على "إنشاء نسخة احتياطية"
- سيتم حفظ الملف في مجلد backups/

### استعادة نسخة احتياطية
- انقر على "استعادة نسخة احتياطية"
- اختر ملف النسخة الاحتياطية (.zip)
- أكد العملية

## الأمان

- كلمات المرور مشفرة
- نسخ احتياطية تلقائية
- تسجيل العمليات
- صلاحيات المستخدمين

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:

- البريد الإلكتروني: <EMAIL>
- الهاتف: +212 XXX XXX XXX

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للمزيد من التفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال طلبات السحب.

## التحديثات المستقبلية

- [ ] دعم قواعد بيانات أخرى (MySQL, PostgreSQL)
- [ ] تطبيق ويب
- [ ] تطبيق موبايل
- [ ] تقارير متقدمة مع الرسوم البيانية
- [ ] إشعارات تلقائية
- [ ] تكامل مع أنظمة أخرى

---

© 2024 Daman School. جميع الحقوق محفوظة.
