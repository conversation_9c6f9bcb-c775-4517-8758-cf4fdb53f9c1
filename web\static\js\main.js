/**
 * الملف الرئيسي للتطبيق
 * Main Application JavaScript
 */

// متغيرات عامة
let currentAcademicYear = '2024-2025';
let currentPage = 'dashboard';

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * تهيئة التطبيق
 */
async function initializeApp() {
    try {
        // تحميل السنوات الدراسية
        await loadAcademicYears();
        
        // تهيئة القائمة الجانبية
        initializeSidebar();
        
        // تهيئة أحداث التطبيق
        initializeEventListeners();
        
        // تحميل الصفحة الحالية
        await loadCurrentPage();
        
        console.log('تم تهيئة التطبيق بنجاح');
    } catch (error) {
        console.error('خطأ في تهيئة التطبيق:', error);
        showAlert('حدث خطأ في تهيئة التطبيق', 'danger');
    }
}

/**
 * تحميل السنوات الدراسية
 */
async function loadAcademicYears() {
    try {
        const years = await api.getAcademicYears();
        const selector = document.getElementById('academicYearSelector');
        
        if (selector) {
            selector.innerHTML = '';
            years.forEach(year => {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                if (year === currentAcademicYear) {
                    option.selected = true;
                }
                selector.appendChild(option);
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل السنوات الدراسية:', error);
    }
}

/**
 * تهيئة القائمة الجانبية
 */
function initializeSidebar() {
    const sidebarLinks = document.querySelectorAll('.sidebar-menu a');

    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const page = this.getAttribute('data-page');

            // إذا كانت الصفحة الرئيسية، منع الانتقال واستخدام SPA
            if (page === 'dashboard') {
                e.preventDefault();

                // إزالة الفئة النشطة من جميع الروابط
                sidebarLinks.forEach(l => l.classList.remove('active'));

                // إضافة الفئة النشطة للرابط المحدد
                this.classList.add('active');

                // التنقل إلى لوحة التحكم
                navigateToPage(page);
            }
            // للصفحات الأخرى، السماح بالانتقال الطبيعي
            // لا نحتاج لمنع الحدث الافتراضي هنا
        });
    });
}

/**
 * تهيئة مستمعي الأحداث
 */
function initializeEventListeners() {
    // تغيير السنة الدراسية
    const academicYearSelector = document.getElementById('academicYearSelector');
    if (academicYearSelector) {
        academicYearSelector.addEventListener('change', function() {
            currentAcademicYear = this.value;
            loadCurrentPage();
        });
    }

    // أزرار التنقل
    document.addEventListener('click', function(e) {
        if (e.target.matches('[data-action]')) {
            const action = e.target.getAttribute('data-action');
            handleAction(action, e.target);
        }
    });

    // نماذج الإرسال
    document.addEventListener('submit', function(e) {
        if (e.target.matches('form[data-form]')) {
            e.preventDefault();
            const formType = e.target.getAttribute('data-form');
            handleFormSubmit(formType, e.target);
        }
    });
}

/**
 * التنقل إلى صفحة
 */
async function navigateToPage(page) {
    currentPage = page;

    try {
        showLoading();

        switch (page) {
            case 'dashboard':
                await loadDashboard();
                break;
            case 'add_incident':
                window.location.href = '/add_incident';
                return;
            case 'incidents':
                window.location.href = '/incidents';
                return;
            case 'institutions':
                window.location.href = '/institutions';
                return;
            case 'reports':
                window.location.href = '/reports';
                return;
            case 'settings':
                window.location.href = '/settings';
                return;
            default:
                console.warn('صفحة غير معروفة:', page);
        }

        hideLoading();
    } catch (error) {
        console.error('خطأ في تحميل الصفحة:', error);
        showAlert('حدث خطأ في تحميل الصفحة', 'danger');
        hideLoading();
    }
}

/**
 * تحميل الصفحة الحالية
 */
async function loadCurrentPage() {
    await navigateToPage(currentPage);
}

/**
 * تحميل لوحة التحكم
 */
async function loadDashboard() {
    try {
        const stats = await api.getDashboardMainStats();
        updateDashboardStats(stats);
    } catch (error) {
        console.error('خطأ في تحميل لوحة التحكم:', error);
        showAlert('حدث خطأ في تحميل الإحصائيات', 'danger');
    }
}



/**
 * تحديث إحصائيات لوحة التحكم
 */
function updateDashboardStats(stats) {
    // تحديث الأرقام الإجمالية
    updateElement('totalIncidents', stats.total_incidents || 0);
    updateElement('pendingIncidents', stats.pending || 0);
    updateElement('resolvedIncidents', stats.resolved || 0);
    updateElement('rejectedIncidents', stats.rejected || 0);
    
    // تحديث آخر الحوادث
    updateRecentIncidents(stats.recent_incidents || []);
}

/**
 * تحديث آخر الحوادث
 */
function updateRecentIncidents(incidents) {
    const container = document.getElementById('recentIncidents');
    if (!container) return;
    
    if (incidents.length === 0) {
        container.innerHTML = '<p class="text-center">لا توجد حوادث مسجلة</p>';
        return;
    }
    
    const html = incidents.map(incident => `
        <div class="incident-item">
            <div class="incident-info">
                <h6>${incident[0]}</h6>
                <p>${incident[1]} - ${formatDate(incident[2])}</p>
            </div>
            <div class="incident-status">
                <span class="badge ${getStatusClass(incident[3])}">${incident[3]}</span>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

/**
 * معالجة الإجراءات
 */
function handleAction(action, element) {
    switch (action) {
        case 'refresh':
            loadCurrentPage();
            break;
        case 'add-incident':
            navigateToPage('add_incident');
            break;
        case 'view-incidents':
            navigateToPage('incidents');
            break;
        case 'view-institutions':
            navigateToPage('institutions');
            break;
        case 'view-reports':
            navigateToPage('reports');
            break;
        case 'view-settings':
            navigateToPage('settings');
            break;
        default:
            console.warn('إجراء غير معروف:', action);
    }
}

/**
 * معالجة إرسال النماذج
 */
async function handleFormSubmit(formType, form) {
    try {
        showLoading();
        
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        switch (formType) {
            case 'incident':
                await handleIncidentForm(data);
                break;
            case 'institution':
                await handleInstitutionForm(data);
                break;
            default:
                console.warn('نوع نموذج غير معروف:', formType);
        }
        
        hideLoading();
    } catch (error) {
        console.error('خطأ في إرسال النموذج:', error);
        showAlert('حدث خطأ في حفظ البيانات', 'danger');
        hideLoading();
    }
}

/**
 * معالجة نموذج الحادثة
 */
async function handleIncidentForm(data) {
    try {
        const result = await api.createIncident(data);
        showAlert('تم حفظ الحادثة بنجاح', 'success');
        
        // إعادة تعيين النموذج
        document.querySelector('form[data-form="incident"]').reset();
        
        // التنقل إلى قائمة الحوادث
        setTimeout(() => {
            navigateToPage('incidents');
        }, 1500);
        
    } catch (error) {
        throw error;
    }
}

/**
 * معالجة نموذج المؤسسة
 */
async function handleInstitutionForm(data) {
    try {
        const result = await api.createInstitution(data);
        showAlert('تم حفظ المؤسسة بنجاح', 'success');
        
        // إعادة تعيين النموذج
        document.querySelector('form[data-form="institution"]').reset();
        
        // إعادة تحميل قائمة المؤسسات
        if (currentPage === 'institutions') {
            loadInstitutions();
        }
        
    } catch (error) {
        throw error;
    }
}

// === وظائف مساعدة ===

/**
 * تحديث محتوى عنصر
 */
function updateElement(id, content) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = content;
    }
}

/**
 * عرض تنبيه
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer') || document.body;
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.innerHTML = `
        <span>${message}</span>
        <button type="button" class="btn-close" onclick="this.parentElement.remove()">×</button>
    `;
    
    alertContainer.appendChild(alert);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 5000);
}

/**
 * عرض شاشة التحميل
 */
function showLoading() {
    const loading = document.getElementById('loadingOverlay');
    if (loading) {
        loading.style.display = 'flex';
    }
}

/**
 * إخفاء شاشة التحميل
 */
function hideLoading() {
    const loading = document.getElementById('loadingOverlay');
    if (loading) {
        loading.style.display = 'none';
    }
}

/**
 * تنسيق التاريخ
 */
function formatDate(dateString) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

/**
 * الحصول على فئة CSS للحالة
 */
function getStatusClass(status) {
    switch (status) {
        case 'تمت التسوية':
            return 'badge-success';
        case 'في طور التسوية':
            return 'badge-warning';
        case 'مرفوض':
            return 'badge-danger';
        case 'تم الدفع':
            return 'badge-info';
        default:
            return 'badge-secondary';
    }
}

/**
 * تأكيد الحذف
 */
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}
