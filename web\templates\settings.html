<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - منصة تدبير الحوادث المدرسية</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading">
            <div class="spinner"></div>
            <p>جاري المعالجة...</p>
        </div>
    </div>

    <!-- حاوي التطبيق -->
    <div class="app-container">
        <!-- الشريط العلوي -->
        <header class="header">
            <div class="header-right">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="app-title">إعدادات التطبيق</h1>
            </div>
            <div class="header-left">
                <a href="/" class="btn btn-outline">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
        </header>

        <!-- القائمة الجانبية -->
        <nav class="sidebar">
            <ul class="sidebar-menu">
                <li>
                    <a href="/" data-page="dashboard">
                        <i class="fas fa-tachometer-alt icon"></i>
                        الرئيسية
                    </a>
                </li>
                <li>
                    <a href="/add_incident" data-page="add_incident">
                        <i class="fas fa-plus-circle icon"></i>
                        إضافة حادثة
                    </a>
                </li>
                <li>
                    <a href="/incidents" data-page="incidents">
                        <i class="fas fa-list icon"></i>
                        قائمة الحوادث
                    </a>
                </li>
                <li>
                    <a href="/institutions" data-page="institutions">
                        <i class="fas fa-school icon"></i>
                        المؤسسات التعليمية
                    </a>
                </li>
                <li>
                    <a href="/reports" data-page="reports">
                        <i class="fas fa-chart-bar icon"></i>
                        التقارير
                    </a>
                </li>
                <li>
                    <a href="/settings" data-page="settings" class="active">
                        <i class="fas fa-cog icon"></i>
                        الإعدادات
                    </a>
                </li>
            </ul>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- حاوي التنبيهات -->
            <div id="alertContainer"></div>

            <!-- إعدادات العرض -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-desktop"></i>
                        إعدادات العرض
                    </h3>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="fontSize" class="form-label">حجم الخط</label>
                            <select id="fontSize" class="form-control">
                                <option value="12">صغير (12px)</option>
                                <option value="14" selected>متوسط (14px)</option>
                                <option value="16">كبير (16px)</option>
                                <option value="18">كبير جداً (18px)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="theme" class="form-label">المظهر</label>
                            <select id="theme" class="form-control">
                                <option value="light" selected>فاتح</option>
                                <option value="dark">داكن</option>
                                <option value="auto">تلقائي</option>
                            </select>
                        </div>
                    </div>
                    <div class="text-center">
                        <button id="saveDisplaySettings" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ إعدادات العرض
                        </button>
                    </div>
                </div>
            </div>

            <!-- النسخ الاحتياطية -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-database"></i>
                        النسخ الاحتياطية
                    </h3>
                </div>
                <div class="card-body">
                    <div class="backup-actions mb-4">
                        <button id="createBackupBtn" class="btn btn-success">
                            <i class="fas fa-plus"></i>
                            إنشاء نسخة احتياطية
                        </button>
                        <button id="restoreBackupBtn" class="btn btn-warning">
                            <i class="fas fa-upload"></i>
                            استعادة نسخة احتياطية
                        </button>
                        <input type="file" id="backupFileInput" accept=".zip" style="display: none;">
                    </div>
                    
                    <h4>النسخ الاحتياطية المتاحة</h4>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>اسم الملف</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الحجم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="backupsTableBody">
                                <tr>
                                    <td colspan="4" class="text-center">
                                        <div class="loading">
                                            <div class="spinner"></div>
                                            <p>جاري تحميل النسخ الاحتياطية...</p>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- استيراد البيانات -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-excel"></i>
                        استيراد البيانات من Excel
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        يمكنك استيراد الحوادث والمؤسسات من ملفات Excel. تأكد من أن الملف يحتوي على الأعمدة المطلوبة.
                    </div>
                    
                    <form id="importForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="importFile" class="form-label">اختر ملف Excel</label>
                                <input type="file" id="importFile" name="file" class="form-control" accept=".xlsx,.xls" required>
                            </div>
                            <div class="form-group">
                                <label for="importAcademicYear" class="form-label">السنة الدراسية</label>
                                <select id="importAcademicYear" name="academic_year" class="form-control" required>
                                    <option value="2024-2025" selected>2024-2025</option>
                                    <option value="2023-2024">2023-2024</option>
                                    <option value="2022-2023">2022-2023</option>
                                    <option value="2021-2022">2021-2022</option>
                                    <option value="2020-2021">2020-2021</option>
                                    <option value="2019-2020">2019-2020</option>
                                    <option value="2018-2019">2018-2019</option>
                                </select>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload"></i>
                                استيراد البيانات
                            </button>
                        </div>
                    </form>
                    
                    <div id="importProgress" class="mt-3" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <p id="progressText" class="text-center mt-2">جاري الاستيراد...</p>
                    </div>
                </div>
            </div>

            <!-- إدارة السنوات الدراسية -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar"></i>
                        إدارة السنوات الدراسية
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        هذه الميزة متاحة فقط لمدير التطبيق
                    </div>
                    
                    <form id="academicYearForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="newAcademicYear" class="form-label">السنة الدراسية الجديدة</label>
                                <input type="text" id="newAcademicYear" name="academic_year" class="form-control" placeholder="مثال: 2025-2026" pattern="[0-9]{4}-[0-9]{4}">
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus"></i>
                                إضافة سنة دراسية
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إدارة المستخدمين -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-users"></i>
                        إدارة المستخدمين
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        هذه الميزة متاحة فقط لمدير التطبيق
                    </div>
                    
                    <div class="user-management">
                        <button id="addUserBtn" class="btn btn-primary mb-3">
                            <i class="fas fa-user-plus"></i>
                            إضافة مستخدم جديد
                        </button>
                        
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>اسم المستخدم</th>
                                        <th>الدور</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <tr>
                                        <td>admin</td>
                                        <td>مدير</td>
                                        <td><span class="badge badge-success">نشط</span></td>
                                        <td>2024-01-01</td>
                                        <td>
                                            <button class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات التطبيق -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle"></i>
                        معلومات التطبيق
                    </h3>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">اسم التطبيق:</label>
                            <p>منصة تدبير الحوادث المدرسية - Daman School</p>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الإصدار:</label>
                            <p>1.0.0</p>
                        </div>
                        <div class="form-group">
                            <label class="form-label">المطور:</label>
                            <p>Daman School Team</p>
                        </div>
                        <div class="form-group">
                            <label class="form-label">تاريخ الإصدار:</label>
                            <p>2024</p>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <button id="checkUpdatesBtn" class="btn btn-secondary">
                            <i class="fas fa-sync-alt"></i>
                            فحص التحديثات
                        </button>
                        <button id="aboutBtn" class="btn btn-outline">
                            <i class="fas fa-question-circle"></i>
                            حول التطبيق
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- JavaScript خاص بصفحة الإعدادات -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeSettingsPage();
        });

        function initializeSettingsPage() {
            // تهيئة إعدادات العرض
            initializeDisplaySettings();
            
            // تهيئة النسخ الاحتياطية
            initializeBackupSettings();
            
            // تهيئة استيراد البيانات
            initializeImportSettings();
            
            // تهيئة إدارة السنوات الدراسية
            initializeAcademicYearSettings();
            
            // تحميل النسخ الاحتياطية
            loadBackupsList();
        }

        function initializeDisplaySettings() {
            const saveBtn = document.getElementById('saveDisplaySettings');
            const fontSizeSelect = document.getElementById('fontSize');
            const themeSelect = document.getElementById('theme');
            
            // تحميل الإعدادات المحفوظة
            const savedFontSize = localStorage.getItem('fontSize') || '14';
            const savedTheme = localStorage.getItem('theme') || 'light';
            
            fontSizeSelect.value = savedFontSize;
            themeSelect.value = savedTheme;
            
            // تطبيق الإعدادات
            applyDisplaySettings(savedFontSize, savedTheme);
            
            saveBtn.addEventListener('click', function() {
                const fontSize = fontSizeSelect.value;
                const theme = themeSelect.value;
                
                // حفظ الإعدادات
                localStorage.setItem('fontSize', fontSize);
                localStorage.setItem('theme', theme);
                
                // تطبيق الإعدادات
                applyDisplaySettings(fontSize, theme);
                
                showAlert('تم حفظ إعدادات العرض بنجاح', 'success');
            });
        }

        function applyDisplaySettings(fontSize, theme) {
            // تطبيق حجم الخط
            document.documentElement.style.setProperty('--base-font-size', fontSize + 'px');
            document.body.style.fontSize = fontSize + 'px';
            
            // تطبيق المظهر
            if (theme === 'dark') {
                document.body.classList.add('dark-theme');
            } else {
                document.body.classList.remove('dark-theme');
            }
        }

        function initializeBackupSettings() {
            const createBtn = document.getElementById('createBackupBtn');
            const restoreBtn = document.getElementById('restoreBackupBtn');
            const fileInput = document.getElementById('backupFileInput');
            
            createBtn.addEventListener('click', createBackup);
            restoreBtn.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', restoreBackup);
        }

        async function createBackup() {
            try {
                showLoading();
                
                // محاكاة إنشاء نسخة احتياطية
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
                loadBackupsList();
                
                hideLoading();
            } catch (error) {
                console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
                showAlert('حدث خطأ في إنشاء النسخة الاحتياطية', 'danger');
                hideLoading();
            }
        }

        async function restoreBackup(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            if (!confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية.')) {
                return;
            }
            
            try {
                showLoading();
                
                // محاكاة استعادة النسخة الاحتياطية
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                showAlert('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                
                hideLoading();
            } catch (error) {
                console.error('خطأ في استعادة النسخة الاحتياطية:', error);
                showAlert('حدث خطأ في استعادة النسخة الاحتياطية', 'danger');
                hideLoading();
            }
        }

        function loadBackupsList() {
            const tbody = document.getElementById('backupsTableBody');
            
            // محاكاة قائمة النسخ الاحتياطية
            const backups = [
                {
                    filename: 'daman_backup_20241201_120000.zip',
                    created_at: '2024-12-01 12:00:00',
                    size: '2.5 MB'
                },
                {
                    filename: 'daman_backup_20241130_180000.zip',
                    created_at: '2024-11-30 18:00:00',
                    size: '2.3 MB'
                }
            ];
            
            if (backups.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center">لا توجد نسخ احتياطية</td>
                    </tr>
                `;
                return;
            }
            
            const html = backups.map(backup => `
                <tr>
                    <td>${backup.filename}</td>
                    <td>${formatDate(backup.created_at)}</td>
                    <td>${backup.size}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="downloadBackup('${backup.filename}')">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteBackup('${backup.filename}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
            
            tbody.innerHTML = html;
        }

        function downloadBackup(filename) {
            showAlert('جاري تحميل النسخة الاحتياطية...', 'info');
            // TODO: تنفيذ تحميل النسخة الاحتياطية
        }

        function deleteBackup(filename) {
            if (!confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
                return;
            }
            
            showAlert('تم حذف النسخة الاحتياطية بنجاح', 'success');
            loadBackupsList();
        }

        function initializeImportSettings() {
            const importForm = document.getElementById('importForm');
            
            importForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const fileInput = document.getElementById('importFile');
                const file = fileInput.files[0];
                
                if (!file) {
                    showAlert('يرجى اختيار ملف للاستيراد', 'warning');
                    return;
                }
                
                try {
                    showImportProgress();
                    
                    // محاكاة عملية الاستيراد
                    for (let i = 0; i <= 100; i += 10) {
                        updateImportProgress(i);
                        await new Promise(resolve => setTimeout(resolve, 200));
                    }
                    
                    hideImportProgress();
                    showAlert('تم استيراد البيانات بنجاح', 'success');
                    importForm.reset();
                    
                } catch (error) {
                    console.error('خطأ في استيراد البيانات:', error);
                    hideImportProgress();
                    showAlert('حدث خطأ في استيراد البيانات', 'danger');
                }
            });
        }

        function showImportProgress() {
            document.getElementById('importProgress').style.display = 'block';
        }

        function hideImportProgress() {
            document.getElementById('importProgress').style.display = 'none';
        }

        function updateImportProgress(percentage) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            progressFill.style.width = percentage + '%';
            progressText.textContent = `جاري الاستيراد... ${percentage}%`;
        }

        function initializeAcademicYearSettings() {
            const academicYearForm = document.getElementById('academicYearForm');
            
            academicYearForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const newYear = document.getElementById('newAcademicYear').value;
                
                if (!newYear) {
                    showAlert('يرجى إدخال السنة الدراسية', 'warning');
                    return;
                }
                
                // التحقق من صحة التنسيق
                const yearPattern = /^\d{4}-\d{4}$/;
                if (!yearPattern.test(newYear)) {
                    showAlert('تنسيق السنة الدراسية غير صحيح. استخدم التنسيق: YYYY-YYYY', 'warning');
                    return;
                }
                
                showAlert('تم إضافة السنة الدراسية بنجاح', 'success');
                academicYearForm.reset();
            });
        }

        // وظائف إضافية
        document.getElementById('checkUpdatesBtn').addEventListener('click', function() {
            showAlert('لا توجد تحديثات متاحة حالياً', 'info');
        });

        document.getElementById('aboutBtn').addEventListener('click', function() {
            alert(`منصة تدبير الحوادث المدرسية - Daman School
الإصدار: 1.0.0
المطور: Daman School Team
© 2024 جميع الحقوق محفوظة`);
        });

        document.getElementById('addUserBtn').addEventListener('click', function() {
            showAlert('ميزة إضافة المستخدمين قيد التطوير', 'info');
        });
    </script>

    <!-- أنماط CSS إضافية -->
    <style>
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: var(--light-color);
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
            width: 0%;
            transition: width 0.3s ease;
        }

        .backup-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .user-management {
            margin-top: 1rem;
        }

        .dark-theme {
            --background-color: #1a1a1a;
            --text-color: #ffffff;
            --primary-color: #4a90e2;
            --light-color: #2d2d2d;
            --border-color: #404040;
        }

        .dark-theme .card {
            background: #2d2d2d;
            color: #ffffff;
        }

        .dark-theme .form-control {
            background: #404040;
            color: #ffffff;
            border-color: #555555;
        }

        .dark-theme .table {
            background: #2d2d2d;
            color: #ffffff;
        }

        .dark-theme .table th {
            background: #404040;
        }

        @media (max-width: 768px) {
            .backup-actions {
                flex-direction: column;
            }
            
            .backup-actions button {
                width: 100%;
            }
        }
    </style>
</body>
</html>
