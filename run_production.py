#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل التطبيق في وضع الإنتاج
Run Application in Production Mode
"""

import os
import sys
import webview
from main import app, create_window

def main():
    """تشغيل التطبيق في وضع الإنتاج"""
    print("========================================")
    print("    منصة تدبير الحوادث المدرسية")
    print("    Daman School Management System")
    print("========================================")
    print()
    print("جاري تشغيل التطبيق...")
    print("Starting application...")
    print()
    
    try:
        # إنشاء النافذة وتشغيل التطبيق
        window = create_window()
        webview.start(debug=False)
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        print(f"Error starting application: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)

if __name__ == '__main__':
    main()
