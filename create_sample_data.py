#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية للتطبيق
Create Sample Data for Testing
"""

import sqlite3
import os
from datetime import datetime, timedelta
import random

# إضافة مسار المشروع
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.database import init_database, get_connection

def create_sample_institutions():
    """إنشاء مؤسسات تجريبية"""
    institutions = [
        {
            'code': 'PRIM001',
            'name': 'مدرسة النور الابتدائية',
            'commune': 'الرباط',
            'environment': 'حضري',
            'level': 'ابتدائي',
            'nature': 'عمومية',
            'director_name': 'أحمد المرابط',
            'phone': '0537123456',
            'email': '<EMAIL>',
            'academic_year': '2024-2025'
        },
        {
            'code': 'COLL001',
            'name': 'ثانوية ابن خلدون الإعدادية',
            'commune': 'سلا',
            'environment': 'حضري',
            'level': 'إعدادي',
            'nature': 'عمومية',
            'director_name': 'فاطمة الزهراء',
            'phone': '0537234567',
            'email': '<EMAIL>',
            'academic_year': '2024-2025'
        },
        {
            'code': 'LYC001',
            'name': 'الثانوية التأهيلية محمد الخامس',
            'commune': 'تمارة',
            'environment': 'حضري',
            'level': 'تأهيلي',
            'nature': 'عمومية',
            'director_name': 'عبد الرحمن الفاسي',
            'phone': '0537345678',
            'email': '<EMAIL>',
            'academic_year': '2024-2025'
        },
        {
            'code': 'PRIM002',
            'name': 'مدرسة الأمل الابتدائية',
            'commune': 'الخميسات',
            'environment': 'قروي',
            'level': 'ابتدائي',
            'nature': 'عمومية',
            'director_name': 'خديجة العلوي',
            'phone': '0537456789',
            'email': '<EMAIL>',
            'academic_year': '2024-2025'
        },
        {
            'code': 'COLL002',
            'name': 'ثانوية الفتح الإعدادية',
            'commune': 'القنيطرة',
            'environment': 'حضري',
            'level': 'إعدادي',
            'nature': 'عمومية',
            'director_name': 'محمد الإدريسي',
            'phone': '0537567890',
            'email': '<EMAIL>',
            'academic_year': '2024-2025'
        }
    ]
    
    conn = get_connection()
    cursor = conn.cursor()
    
    for inst in institutions:
        try:
            cursor.execute('''
                INSERT OR IGNORE INTO institutions (
                    code, name, commune, environment, level, nature,
                    director_name, phone, email, academic_year
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                inst['code'], inst['name'], inst['commune'], inst['environment'],
                inst['level'], inst['nature'], inst['director_name'],
                inst['phone'], inst['email'], inst['academic_year']
            ))
        except Exception as e:
            print(f"خطأ في إدراج المؤسسة {inst['name']}: {e}")
    
    conn.commit()
    conn.close()
    print("تم إنشاء المؤسسات التجريبية بنجاح")

def create_sample_incidents():
    """إنشاء حوادث تجريبية"""
    
    # أسماء تجريبية
    male_names = [
        'أحمد محمد العلوي', 'محمد أحمد الفاسي', 'عبد الله يوسف المرابط',
        'يوسف عبد الرحمن الإدريسي', 'عمر خالد الحسني', 'خالد عمر الشريف',
        'سعد محمد الزهراني', 'محمود أحمد القرشي', 'إبراهيم يوسف العثماني'
    ]
    
    female_names = [
        'فاطمة الزهراء المرابط', 'عائشة محمد الفاسي', 'خديجة أحمد العلوي',
        'مريم يوسف الإدريسي', 'زينب عبد الله الحسني', 'أمينة محمد الشريف',
        'سعاد أحمد الزهراني', 'نادية محمود القرشي', 'ليلى إبراهيم العثماني'
    ]
    
    institutions = ['PRIM001', 'COLL001', 'LYC001', 'PRIM002', 'COLL002']
    institution_names = [
        'مدرسة النور الابتدائية', 'ثانوية ابن خلدون الإعدادية',
        'الثانوية التأهيلية محمد الخامس', 'مدرسة الأمل الابتدائية',
        'ثانوية الفتح الإعدادية'
    ]
    
    levels = ['ابتدائي', 'إعدادي', 'تأهيلي']
    environments = ['حضري', 'قروي']
    statuses = ['في طور التسوية', 'تمت التسوية', 'مرفوض', 'تم الدفع']
    incident_types = ['حادثة مدرسية', 'حادثة تنقل', 'حادثة رياضية']
    severities = ['عادية', 'خطيرة', 'وفاة']
    
    conn = get_connection()
    cursor = conn.cursor()
    
    # إنشاء 50 حادثة تجريبية
    for i in range(1, 51):
        gender = random.choice(['ذكر', 'أنثى'])
        student_name = random.choice(male_names if gender == 'ذكر' else female_names)
        
        institution_idx = random.randint(0, len(institutions) - 1)
        institution_code = institutions[institution_idx]
        institution_name = institution_names[institution_idx]
        
        # تحديد السلك بناءً على المؤسسة
        if 'PRIM' in institution_code:
            level = 'ابتدائي'
        elif 'COLL' in institution_code:
            level = 'إعدادي'
        else:
            level = 'تأهيلي'
        
        environment = random.choice(environments)
        status = random.choice(statuses)
        incident_type = random.choice(incident_types)
        severity = random.choice(severities)
        
        # تواريخ عشوائية في آخر 6 أشهر
        incident_date = datetime.now() - timedelta(days=random.randint(1, 180))
        settlement_date = incident_date + timedelta(days=random.randint(1, 30))
        
        file_number = f"2024-{i:04d}"
        reference = f"REF-{random.randint(1000, 9999)}"
        student_id = f"ST{random.randint(100000, 999999)}"
        
        try:
            cursor.execute('''
                INSERT INTO incidents (
                    file_number, student_name, student_id, gender, institution_name,
                    institution_code, environment, level, reference, incident_date,
                    settlement_date, status, incident_type, severity, academic_year
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                file_number, student_name, student_id, gender, institution_name,
                institution_code, environment, level, reference,
                incident_date.strftime('%Y-%m-%d'),
                settlement_date.strftime('%Y-%m-%d'),
                status, incident_type, severity, '2024-2025'
            ))
            
            incident_id = cursor.lastrowid
            
            # إضافة مستفيدين عشوائيين
            if random.choice([True, False]):  # 50% احتمال وجود مستفيدين
                relationships = ['الأب', 'الأم', 'الولي']
                beneficiary_names = [
                    'محمد أحمد المرابط', 'فاطمة الزهراء الفاسي', 'عبد الله يوسف العلوي'
                ]
                
                for j in range(random.randint(1, 2)):  # 1-2 مستفيد
                    cursor.execute('''
                        INSERT INTO beneficiaries (incident_id, relationship, name, phone)
                        VALUES (?, ?, ?, ?)
                    ''', (
                        incident_id,
                        random.choice(relationships),
                        random.choice(beneficiary_names),
                        f"06{random.randint(10000000, 99999999)}"
                    ))
            
        except Exception as e:
            print(f"خطأ في إدراج الحادثة {i}: {e}")
    
    conn.commit()
    conn.close()
    print("تم إنشاء الحوادث التجريبية بنجاح")

def main():
    """الدالة الرئيسية"""
    print("إنشاء البيانات التجريبية...")
    print("Creating sample data...")
    
    # تهيئة قاعدة البيانات
    init_database()
    
    # إنشاء المؤسسات التجريبية
    create_sample_institutions()
    
    # إنشاء الحوادث التجريبية
    create_sample_incidents()
    
    print("\nتم إنشاء جميع البيانات التجريبية بنجاح!")
    print("Sample data created successfully!")
    print("\nيمكنك الآن تشغيل التطبيق واستكشاف الميزات")
    print("You can now run the application and explore the features")

if __name__ == '__main__':
    main()
