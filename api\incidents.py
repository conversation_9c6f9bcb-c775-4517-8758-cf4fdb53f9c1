#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API للحوادث المدرسية
Incidents API
"""

from flask import Blueprint, request, jsonify
import sqlite3
from datetime import datetime
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import get_connection, get_next_file_number

incidents_bp = Blueprint('incidents', __name__)

@incidents_bp.route('/', methods=['GET'])
def get_incidents():
    """الحصول على قائمة الحوادث مع التصفية"""
    try:
        # معاملات التصفية
        academic_year = request.args.get('academic_year')
        gender = request.args.get('gender')
        environment = request.args.get('environment')
        level = request.args.get('level')
        incident_type = request.args.get('incident_type')
        severity = request.args.get('severity')
        status = request.args.get('status')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        search = request.args.get('search')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        conn = get_connection()
        cursor = conn.cursor()
        
        # بناء الاستعلام
        query = '''
            SELECT i.*, 
                   GROUP_CONCAT(b.name || ' (' || b.relationship || ')') as beneficiaries
            FROM incidents i
            LEFT JOIN beneficiaries b ON i.id = b.incident_id
            WHERE 1=1
        '''
        params = []
        
        if academic_year:
            query += ' AND i.academic_year = ?'
            params.append(academic_year)
        
        if gender:
            query += ' AND i.gender = ?'
            params.append(gender)
        
        if environment:
            query += ' AND i.environment = ?'
            params.append(environment)
        
        if level:
            query += ' AND i.level = ?'
            params.append(level)
        
        if incident_type:
            query += ' AND i.incident_type = ?'
            params.append(incident_type)
        
        if severity:
            query += ' AND i.severity = ?'
            params.append(severity)
        
        if status:
            query += ' AND i.status = ?'
            params.append(status)
        
        if date_from:
            query += ' AND i.incident_date >= ?'
            params.append(date_from)
        
        if date_to:
            query += ' AND i.incident_date <= ?'
            params.append(date_to)
        
        if search:
            query += ' AND (i.student_name LIKE ? OR i.institution_name LIKE ? OR i.file_number LIKE ?)'
            search_param = f'%{search}%'
            params.extend([search_param, search_param, search_param])
        
        query += ' GROUP BY i.id ORDER BY i.created_at DESC'
        
        # تنفيذ الاستعلام مع التصفح
        offset = (page - 1) * per_page
        query += f' LIMIT {per_page} OFFSET {offset}'
        
        cursor.execute(query, params)
        incidents = [dict(row) for row in cursor.fetchall()]
        
        # عدد النتائج الإجمالي
        count_query = query.replace('SELECT i.*, GROUP_CONCAT(b.name || \' (\' || b.relationship || \')\') as beneficiaries', 'SELECT COUNT(DISTINCT i.id)')
        count_query = count_query.split('GROUP BY')[0]
        count_query = count_query.split('ORDER BY')[0]
        count_query = count_query.split('LIMIT')[0]
        
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]
        
        conn.close()
        
        return jsonify({
            'incidents': incidents,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@incidents_bp.route('/', methods=['POST'])
def create_incident():
    """إنشاء حادثة جديدة"""
    try:
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        required_fields = ['student_name', 'gender', 'institution_name', 'environment', 
                          'level', 'incident_date', 'settlement_date', 'status', 'incident_type']
        
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'الحقل {field} مطلوب'}), 400
        
        conn = get_connection()
        cursor = conn.cursor()
        
        # توليد رقم الملف
        file_number = get_next_file_number()
        
        # إدراج الحادثة
        cursor.execute('''
            INSERT INTO incidents (
                file_number, student_name, student_id, gender, institution_name,
                institution_code, environment, level, reference, incident_date,
                settlement_date, status, incident_type, severity, academic_year
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            file_number,
            data['student_name'],
            data.get('student_id'),
            data['gender'],
            data['institution_name'],
            data.get('institution_code'),
            data['environment'],
            data['level'],
            data.get('reference'),
            data['incident_date'],
            data['settlement_date'],
            data['status'],
            data['incident_type'],
            data.get('severity'),
            data.get('academic_year', '2024-2025')
        ))
        
        incident_id = cursor.lastrowid
        
        # إدراج المستفيدين
        beneficiaries = data.get('beneficiaries', [])
        for beneficiary in beneficiaries:
            if beneficiary.get('name') or beneficiary.get('relationship'):
                cursor.execute('''
                    INSERT INTO beneficiaries (incident_id, relationship, name, phone)
                    VALUES (?, ?, ?, ?)
                ''', (
                    incident_id,
                    beneficiary.get('relationship'),
                    beneficiary.get('name'),
                    beneficiary.get('phone')
                ))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'message': 'تم إنشاء الحادثة بنجاح',
            'incident_id': incident_id,
            'file_number': file_number
        }), 201
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@incidents_bp.route('/<int:incident_id>', methods=['GET'])
def get_incident(incident_id):
    """الحصول على تفاصيل حادثة محددة"""
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # الحصول على الحادثة
        cursor.execute('SELECT * FROM incidents WHERE id = ?', (incident_id,))
        incident = cursor.fetchone()
        
        if not incident:
            return jsonify({'error': 'الحادثة غير موجودة'}), 404
        
        incident = dict(incident)
        
        # الحصول على المستفيدين
        cursor.execute('SELECT * FROM beneficiaries WHERE incident_id = ?', (incident_id,))
        beneficiaries = [dict(row) for row in cursor.fetchall()]
        
        incident['beneficiaries'] = beneficiaries
        
        conn.close()
        
        return jsonify(incident)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@incidents_bp.route('/<int:incident_id>', methods=['PUT'])
def update_incident(incident_id):
    """تحديث حادثة"""
    try:
        data = request.get_json()
        
        conn = get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود الحادثة
        cursor.execute('SELECT id FROM incidents WHERE id = ?', (incident_id,))
        if not cursor.fetchone():
            return jsonify({'error': 'الحادثة غير موجودة'}), 404
        
        # تحديث الحادثة
        update_fields = []
        params = []
        
        updatable_fields = [
            'student_name', 'student_id', 'gender', 'institution_name',
            'institution_code', 'environment', 'level', 'reference',
            'incident_date', 'settlement_date', 'status', 'incident_type', 'severity'
        ]
        
        for field in updatable_fields:
            if field in data:
                update_fields.append(f'{field} = ?')
                params.append(data[field])
        
        if update_fields:
            update_fields.append('updated_at = CURRENT_TIMESTAMP')
            params.append(incident_id)
            
            query = f'UPDATE incidents SET {", ".join(update_fields)} WHERE id = ?'
            cursor.execute(query, params)
        
        # تحديث المستفيدين
        if 'beneficiaries' in data:
            # حذف المستفيدين الحاليين
            cursor.execute('DELETE FROM beneficiaries WHERE incident_id = ?', (incident_id,))
            
            # إضافة المستفيدين الجدد
            for beneficiary in data['beneficiaries']:
                if beneficiary.get('name') or beneficiary.get('relationship'):
                    cursor.execute('''
                        INSERT INTO beneficiaries (incident_id, relationship, name, phone)
                        VALUES (?, ?, ?, ?)
                    ''', (
                        incident_id,
                        beneficiary.get('relationship'),
                        beneficiary.get('name'),
                        beneficiary.get('phone')
                    ))
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': 'تم تحديث الحادثة بنجاح'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@incidents_bp.route('/<int:incident_id>', methods=['DELETE'])
def delete_incident(incident_id):
    """حذف حادثة"""
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود الحادثة
        cursor.execute('SELECT id FROM incidents WHERE id = ?', (incident_id,))
        if not cursor.fetchone():
            return jsonify({'error': 'الحادثة غير موجودة'}), 404
        
        # حذف الحادثة (سيتم حذف المستفيدين تلقائياً بسبب CASCADE)
        cursor.execute('DELETE FROM incidents WHERE id = ?', (incident_id,))
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': 'تم حذف الحادثة بنجاح'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
