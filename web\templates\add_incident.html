<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة حادثة جديدة - منصة تدبير الحوادث المدرسية</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading">
            <div class="spinner"></div>
            <p>جاري الحفظ...</p>
        </div>
    </div>

    <!-- حاوي التطبيق -->
    <div class="app-container">
        <!-- الشريط العلوي -->
        <header class="header">
            <div class="header-right">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="app-title">إضافة حادثة جديدة</h1>
            </div>
            <div class="header-left">
                <select id="academicYearSelector" class="academic-year-selector">
                    <option value="2024-2025">2024-2025</option>
                </select>
                <a href="/" class="btn btn-outline">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
        </header>

        <!-- القائمة الجانبية -->
        <nav class="sidebar">
            <ul class="sidebar-menu">
                <li>
                    <a href="/" data-page="dashboard">
                        <i class="fas fa-tachometer-alt icon"></i>
                        الرئيسية
                    </a>
                </li>
                <li>
                    <a href="/add_incident" data-page="add_incident" class="active">
                        <i class="fas fa-plus-circle icon"></i>
                        إضافة حادثة
                    </a>
                </li>
                <li>
                    <a href="/incidents" data-page="incidents">
                        <i class="fas fa-list icon"></i>
                        قائمة الحوادث
                    </a>
                </li>
                <li>
                    <a href="/institutions" data-page="institutions">
                        <i class="fas fa-school icon"></i>
                        المؤسسات التعليمية
                    </a>
                </li>
                <li>
                    <a href="/reports" data-page="reports">
                        <i class="fas fa-chart-bar icon"></i>
                        التقارير
                    </a>
                </li>
                <li>
                    <a href="/settings" data-page="settings">
                        <i class="fas fa-cog icon"></i>
                        الإعدادات
                    </a>
                </li>
            </ul>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- حاوي التنبيهات -->
            <div id="alertContainer"></div>

            <!-- نموذج إضافة الحادثة -->
            <form id="incidentForm" data-form="incident" class="incident-form">
                <!-- معلومات المتضرر -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user"></i>
                            معلومات المتضرر
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="studentName" class="form-label">الاسم والنسب *</label>
                                <input type="text" id="studentName" name="student_name" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="studentId" class="form-label">رقم مسار</label>
                                <input type="text" id="studentId" name="student_id" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="gender" class="form-label">النوع *</label>
                                <select id="gender" name="gender" class="form-control" required>
                                    <option value="">اختر النوع</option>
                                    <option value="ذكر">ذكر</option>
                                    <option value="أنثى">أنثى</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات المؤسسة -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-school"></i>
                            معلومات المؤسسة
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="institutionName" class="form-label">اسم المؤسسة *</label>
                                <input type="text" id="institutionName" name="institution_name" class="form-control" required>
                                <div id="institutionSuggestions" class="suggestions-dropdown"></div>
                            </div>
                            <div class="form-group">
                                <label for="institutionCode" class="form-label">رمز المؤسسة</label>
                                <input type="text" id="institutionCode" name="institution_code" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="environment" class="form-label">الوسط *</label>
                                <select id="environment" name="environment" class="form-control" required>
                                    <option value="">اختر الوسط</option>
                                    <option value="قروي">قروي</option>
                                    <option value="حضري">حضري</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="level" class="form-label">السلك *</label>
                                <select id="level" name="level" class="form-control" required>
                                    <option value="">اختر السلك</option>
                                    <option value="ابتدائي">ابتدائي</option>
                                    <option value="إعدادي">إعدادي</option>
                                    <option value="تأهيلي">تأهيلي</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات الحادثة -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-exclamation-triangle"></i>
                            معلومات الحادثة
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="reference" class="form-label">المرجع</label>
                                <input type="text" id="reference" name="reference" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="incidentDate" class="form-label">تاريخ الحادثة *</label>
                                <input type="date" id="incidentDate" name="incident_date" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="settlementDate" class="form-label">تاريخ التسوية *</label>
                                <input type="date" id="settlementDate" name="settlement_date" class="form-control" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="status" class="form-label">الحالة *</label>
                                <select id="status" name="status" class="form-control" required>
                                    <option value="">اختر الحالة</option>
                                    <option value="في طور التسوية">في طور التسوية</option>
                                    <option value="تمت التسوية">تمت التسوية</option>
                                    <option value="مرفوض">مرفوض</option>
                                    <option value="تم الدفع">تم الدفع</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="incidentType" class="form-label">نوع الحادثة *</label>
                                <select id="incidentType" name="incident_type" class="form-control" required>
                                    <option value="">اختر نوع الحادثة</option>
                                    <option value="حادثة مدرسية">حادثة مدرسية</option>
                                    <option value="حادثة تنقل">حادثة تنقل</option>
                                    <option value="حادثة رياضية">حادثة رياضية</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="severity" class="form-label">خطورة الحادثة</label>
                                <select id="severity" name="severity" class="form-control">
                                    <option value="">اختر الخطورة</option>
                                    <option value="عادية">عادية</option>
                                    <option value="خطيرة">خطيرة</option>
                                    <option value="وفاة">وفاة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المستفيدون من التعويض -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-users"></i>
                            المستفيدون من التعويض
                        </h3>
                    </div>
                    <div class="card-body">
                        <div id="beneficiariesContainer">
                            <div class="beneficiary-item">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">العلاقة</label>
                                        <select name="beneficiaries[0][relationship]" class="form-control">
                                            <option value="">اختر العلاقة</option>
                                            <option value="الأب">الأب</option>
                                            <option value="الأم">الأم</option>
                                            <option value="الولي">الولي</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">الاسم والنسب</label>
                                        <input type="text" name="beneficiaries[0][name]" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">الهاتف النقال</label>
                                        <input type="tel" name="beneficiaries[0][phone]" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <button type="button" class="btn btn-danger btn-sm remove-beneficiary" style="margin-top: 1.8rem;">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" id="addBeneficiary" class="btn btn-secondary">
                            <i class="fas fa-plus"></i>
                            إضافة مستفيد آخر
                        </button>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save"></i>
                            حفظ الحادثة
                        </button>
                        <button type="reset" class="btn btn-secondary btn-lg">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                        <a href="/incidents" class="btn btn-outline btn-lg">
                            <i class="fas fa-list"></i>
                            عرض جميع الحوادث
                        </a>
                    </div>
                </div>
            </form>
        </main>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- JavaScript خاص بصفحة إضافة الحادثة -->
    <script>
        let beneficiaryCount = 1;

        document.addEventListener('DOMContentLoaded', function() {
            initializeIncidentForm();
        });

        function initializeIncidentForm() {
            // تهيئة البحث في المؤسسات
            initializeInstitutionSearch();
            
            // تهيئة إضافة المستفيدين
            initializeBeneficiaries();
            
            // تعيين التاريخ الحالي كافتراضي
            setDefaultDates();
        }

        function initializeInstitutionSearch() {
            const institutionInput = document.getElementById('institutionName');
            const suggestionsDiv = document.getElementById('institutionSuggestions');
            
            institutionInput.addEventListener('input', async function() {
                const query = this.value.trim();
                
                if (query.length < 2) {
                    suggestionsDiv.innerHTML = '';
                    return;
                }
                
                try {
                    const institutions = await api.searchInstitutions(query, currentAcademicYear);
                    displayInstitutionSuggestions(institutions, suggestionsDiv);
                } catch (error) {
                    console.error('خطأ في البحث عن المؤسسات:', error);
                }
            });
        }

        function displayInstitutionSuggestions(institutions, container) {
            if (institutions.length === 0) {
                container.innerHTML = '';
                return;
            }
            
            const html = institutions.map(inst => `
                <div class="suggestion-item" onclick="selectInstitution('${inst.code}', '${inst.name}', '${inst.level}', '${inst.environment}')">
                    <strong>${inst.name}</strong>
                    <small>${inst.level} - ${inst.environment}</small>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        function selectInstitution(code, name, level, environment) {
            document.getElementById('institutionName').value = name;
            document.getElementById('institutionCode').value = code;
            document.getElementById('level').value = level;
            document.getElementById('environment').value = environment;
            document.getElementById('institutionSuggestions').innerHTML = '';
        }

        function initializeBeneficiaries() {
            const addButton = document.getElementById('addBeneficiary');
            const container = document.getElementById('beneficiariesContainer');
            
            addButton.addEventListener('click', function() {
                addBeneficiaryItem();
            });
            
            container.addEventListener('click', function(e) {
                if (e.target.closest('.remove-beneficiary')) {
                    removeBeneficiaryItem(e.target.closest('.beneficiary-item'));
                }
            });
        }

        function addBeneficiaryItem() {
            const container = document.getElementById('beneficiariesContainer');
            const newItem = document.createElement('div');
            newItem.className = 'beneficiary-item';
            newItem.innerHTML = `
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">العلاقة</label>
                        <select name="beneficiaries[${beneficiaryCount}][relationship]" class="form-control">
                            <option value="">اختر العلاقة</option>
                            <option value="الأب">الأب</option>
                            <option value="الأم">الأم</option>
                            <option value="الولي">الولي</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">الاسم والنسب</label>
                        <input type="text" name="beneficiaries[${beneficiaryCount}][name]" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">الهاتف النقال</label>
                        <input type="tel" name="beneficiaries[${beneficiaryCount}][phone]" class="form-control">
                    </div>
                    <div class="form-group">
                        <button type="button" class="btn btn-danger btn-sm remove-beneficiary" style="margin-top: 1.8rem;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            
            container.appendChild(newItem);
            beneficiaryCount++;
        }

        function removeBeneficiaryItem(item) {
            const container = document.getElementById('beneficiariesContainer');
            if (container.children.length > 1) {
                item.remove();
            } else {
                showAlert('يجب الاحتفاظ بمستفيد واحد على الأقل', 'warning');
            }
        }

        function setDefaultDates() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('incidentDate').value = today;
            document.getElementById('settlementDate').value = today;
        }
    </script>

    <!-- أنماط CSS إضافية -->
    <style>
        .suggestions-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            box-shadow: var(--shadow);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
        }

        .suggestion-item {
            padding: 0.75rem;
            cursor: pointer;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .suggestion-item:hover {
            background: var(--light-color);
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-item strong {
            display: block;
            color: var(--primary-color);
        }

        .suggestion-item small {
            color: #666;
            font-size: 0.8rem;
        }

        .beneficiary-item {
            margin-bottom: 1rem;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: rgba(52, 152, 219, 0.02);
        }

        .beneficiary-item:last-child {
            margin-bottom: 0;
        }

        .form-group {
            position: relative;
        }

        .incident-form .card {
            margin-bottom: 1.5rem;
        }

        .incident-form .card:last-child {
            margin-bottom: 0;
        }
    </style>
</body>
</html>
