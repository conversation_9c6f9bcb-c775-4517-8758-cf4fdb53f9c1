#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API للمؤسسات التعليمية
Institutions API
"""

from flask import Blueprint, request, jsonify
import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import get_connection

institutions_bp = Blueprint('institutions', __name__)

@institutions_bp.route('/', methods=['GET'])
def get_institutions():
    """الحصول على قائمة المؤسسات مع التصفية"""
    try:
        # معاملات التصفية
        academic_year = request.args.get('academic_year')
        level = request.args.get('level')
        commune = request.args.get('commune')
        environment = request.args.get('environment')
        nature = request.args.get('nature')
        search = request.args.get('search')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        conn = get_connection()
        cursor = conn.cursor()
        
        # بناء الاستعلام
        query = 'SELECT * FROM institutions WHERE 1=1'
        params = []
        
        if academic_year:
            query += ' AND academic_year = ?'
            params.append(academic_year)
        
        if level:
            query += ' AND level = ?'
            params.append(level)
        
        if commune:
            query += ' AND commune LIKE ?'
            params.append(f'%{commune}%')
        
        if environment:
            query += ' AND environment = ?'
            params.append(environment)
        
        if nature:
            query += ' AND nature LIKE ?'
            params.append(f'%{nature}%')
        
        if search:
            query += ' AND (name LIKE ? OR code LIKE ? OR director_name LIKE ?)'
            search_param = f'%{search}%'
            params.extend([search_param, search_param, search_param])
        
        query += ' ORDER BY name'
        
        # تنفيذ الاستعلام مع التصفح
        offset = (page - 1) * per_page
        query += f' LIMIT {per_page} OFFSET {offset}'
        
        cursor.execute(query, params)
        institutions = [dict(row) for row in cursor.fetchall()]
        
        # عدد النتائج الإجمالي
        count_query = query.replace('SELECT *', 'SELECT COUNT(*)')
        count_query = count_query.split('ORDER BY')[0]
        count_query = count_query.split('LIMIT')[0]
        
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]
        
        conn.close()
        
        return jsonify({
            'institutions': institutions,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@institutions_bp.route('/', methods=['POST'])
def create_institution():
    """إنشاء مؤسسة تعليمية جديدة"""
    try:
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        required_fields = ['code', 'name', 'environment', 'level']
        
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'الحقل {field} مطلوب'}), 400
        
        conn = get_connection()
        cursor = conn.cursor()
        
        # التحقق من عدم تكرار الرمز
        cursor.execute('SELECT id FROM institutions WHERE code = ?', (data['code'],))
        if cursor.fetchone():
            return jsonify({'error': 'رمز المؤسسة موجود مسبقاً'}), 400
        
        # إدراج المؤسسة
        cursor.execute('''
            INSERT INTO institutions (
                code, name, commune, environment, level, nature,
                director_name, phone, email, academic_year
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['code'],
            data['name'],
            data.get('commune'),
            data['environment'],
            data['level'],
            data.get('nature'),
            data.get('director_name'),
            data.get('phone'),
            data.get('email'),
            data.get('academic_year', '2024-2025')
        ))
        
        institution_id = cursor.lastrowid
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'message': 'تم إنشاء المؤسسة بنجاح',
            'institution_id': institution_id
        }), 201
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@institutions_bp.route('/<int:institution_id>', methods=['GET'])
def get_institution(institution_id):
    """الحصول على تفاصيل مؤسسة محددة"""
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM institutions WHERE id = ?', (institution_id,))
        institution = cursor.fetchone()
        
        if not institution:
            return jsonify({'error': 'المؤسسة غير موجودة'}), 404
        
        institution = dict(institution)
        
        # الحصول على إحصائيات الحوادث للمؤسسة
        cursor.execute('''
            SELECT COUNT(*) as total_incidents,
                   SUM(CASE WHEN status = 'في طور التسوية' THEN 1 ELSE 0 END) as pending,
                   SUM(CASE WHEN status = 'تمت التسوية' THEN 1 ELSE 0 END) as resolved,
                   SUM(CASE WHEN status = 'مرفوض' THEN 1 ELSE 0 END) as rejected
            FROM incidents 
            WHERE institution_code = ?
        ''', (institution['code'],))
        
        stats = dict(cursor.fetchone())
        institution['stats'] = stats
        
        conn.close()
        
        return jsonify(institution)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@institutions_bp.route('/<int:institution_id>', methods=['PUT'])
def update_institution(institution_id):
    """تحديث مؤسسة"""
    try:
        data = request.get_json()
        
        conn = get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود المؤسسة
        cursor.execute('SELECT code FROM institutions WHERE id = ?', (institution_id,))
        result = cursor.fetchone()
        if not result:
            return jsonify({'error': 'المؤسسة غير موجودة'}), 404
        
        current_code = result[0]
        
        # التحقق من عدم تكرار الرمز (إذا تم تغييره)
        if 'code' in data and data['code'] != current_code:
            cursor.execute('SELECT id FROM institutions WHERE code = ? AND id != ?', 
                          (data['code'], institution_id))
            if cursor.fetchone():
                return jsonify({'error': 'رمز المؤسسة موجود مسبقاً'}), 400
        
        # تحديث المؤسسة
        update_fields = []
        params = []
        
        updatable_fields = [
            'code', 'name', 'commune', 'environment', 'level', 'nature',
            'director_name', 'phone', 'email'
        ]
        
        for field in updatable_fields:
            if field in data:
                update_fields.append(f'{field} = ?')
                params.append(data[field])
        
        if update_fields:
            update_fields.append('updated_at = CURRENT_TIMESTAMP')
            params.append(institution_id)
            
            query = f'UPDATE institutions SET {", ".join(update_fields)} WHERE id = ?'
            cursor.execute(query, params)
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': 'تم تحديث المؤسسة بنجاح'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@institutions_bp.route('/<int:institution_id>', methods=['DELETE'])
def delete_institution(institution_id):
    """حذف مؤسسة"""
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود المؤسسة
        cursor.execute('SELECT code FROM institutions WHERE id = ?', (institution_id,))
        result = cursor.fetchone()
        if not result:
            return jsonify({'error': 'المؤسسة غير موجودة'}), 404
        
        institution_code = result[0]
        
        # التحقق من وجود حوادث مرتبطة بالمؤسسة
        cursor.execute('SELECT COUNT(*) FROM incidents WHERE institution_code = ?', (institution_code,))
        incidents_count = cursor.fetchone()[0]
        
        if incidents_count > 0:
            return jsonify({
                'error': f'لا يمكن حذف المؤسسة لأنها مرتبطة بـ {incidents_count} حادثة'
            }), 400
        
        # حذف المؤسسة
        cursor.execute('DELETE FROM institutions WHERE id = ?', (institution_id,))
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': 'تم حذف المؤسسة بنجاح'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@institutions_bp.route('/search', methods=['GET'])
def search_institutions():
    """البحث في المؤسسات للاستخدام في النماذج"""
    try:
        query = request.args.get('q', '')
        academic_year = request.args.get('academic_year')
        
        conn = get_connection()
        cursor = conn.cursor()
        
        sql = '''
            SELECT code, name, level, environment 
            FROM institutions 
            WHERE (name LIKE ? OR code LIKE ?)
        '''
        params = [f'%{query}%', f'%{query}%']
        
        if academic_year:
            sql += ' AND academic_year = ?'
            params.append(academic_year)
        
        sql += ' ORDER BY name LIMIT 10'
        
        cursor.execute(sql, params)
        institutions = [dict(row) for row in cursor.fetchall()]
        
        conn.close()
        
        return jsonify(institutions)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
