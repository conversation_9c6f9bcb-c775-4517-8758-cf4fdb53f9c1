#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات التطبيق
Application Settings
"""

import os
from datetime import datetime

# إعدادات التطبيق
APP_NAME = "منصة تدبير الحوادث المدرسية"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Daman School"

# إعدادات قاعدة البيانات
DATABASE_NAME = "daman_school.db"
DATABASE_PATH = os.path.join("database", DATABASE_NAME)

# إعدادات الواجهة
DEFAULT_FONT_SIZE = 14
SUPPORTED_LANGUAGES = ["ar", "fr", "en"]
DEFAULT_LANGUAGE = "ar"

# إعدادات السنوات الدراسية
START_ACADEMIC_YEAR = 2018
CURRENT_YEAR = datetime.now().year

# إعدادات النسخ الاحتياطية
BACKUP_DIRECTORY = "backups"
MAX_BACKUP_FILES = 10

# إعدادات الاستيراد
SUPPORTED_EXCEL_FORMATS = [".xlsx", ".xls"]
MAX_IMPORT_ROWS = 10000

# إعدادات التقارير
REPORT_FORMATS = ["PDF", "Excel", "CSV"]

# قوائم البيانات الثابتة
GENDERS = ["ذكر", "أنثى"]
ENVIRONMENTS = ["قروي", "حضري"]
EDUCATION_LEVELS = ["ابتدائي", "إعدادي", "تأهيلي"]
INCIDENT_TYPES = ["حادثة مدرسية", "حادثة تنقل", "حادثة رياضية"]
INCIDENT_SEVERITIES = ["عادية", "خطيرة", "وفاة"]
INCIDENT_STATUSES = ["في طور التسوية", "تمت التسوية", "مرفوض", "تم الدفع"]
BENEFICIARY_RELATIONSHIPS = ["الأب", "الأم", "الولي"]
USER_ROLES = ["مدير", "مستخدم"]

# إعدادات الألوان (CSS Variables)
COLORS = {
    "primary": "#2c3e50",
    "secondary": "#3498db", 
    "success": "#27ae60",
    "warning": "#f39c12",
    "danger": "#e74c3c",
    "light": "#ecf0f1",
    "dark": "#2c3e50",
    "background": "#f8f9fa",
    "text": "#2c3e50"
}

def get_academic_years():
    """الحصول على قائمة السنوات الدراسية"""
    years = []
    for year in range(START_ACADEMIC_YEAR, CURRENT_YEAR + 2):
        years.append(f"{year}-{year + 1}")
    return years

def get_current_academic_year():
    """الحصول على السنة الدراسية الحالية"""
    current_date = datetime.now()
    if current_date.month >= 9:  # من سبتمبر
        return f"{current_date.year}-{current_date.year + 1}"
    else:  # قبل سبتمبر
        return f"{current_date.year - 1}-{current_date.year}"
