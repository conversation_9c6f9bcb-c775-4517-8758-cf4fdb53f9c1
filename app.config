[APPLICATION]
name = منصة تدبير الحوادث المدرسية
name_en = Daman School Management System
version = 1.0.0
author = Daman School Team
description = نظام إدارة الحوادث المدرسية
description_en = School Incidents Management System

[DATABASE]
type = sqlite
path = database/daman_school.db
backup_enabled = true
backup_interval = 24
max_backups = 10

[INTERFACE]
language = ar
direction = rtl
theme = light
font_size = 14
show_splash = true

[FEATURES]
enable_reports = true
enable_export = true
enable_import = true
enable_backup = true
enable_users = true

[SECURITY]
password_min_length = 6
session_timeout = 3600
max_login_attempts = 5
enable_logging = true

[PATHS]
backup_directory = backups
logs_directory = logs
temp_directory = temp
uploads_directory = uploads

[ACADEMIC_YEARS]
start_year = 2018
current_year = 2024
auto_create_years = true

[EXPORT]
formats = csv,excel,pdf
max_records = 10000
include_charts = true

[IMPORT]
supported_formats = xlsx,xls
max_file_size = 50MB
validate_data = true
create_backup_before_import = true
