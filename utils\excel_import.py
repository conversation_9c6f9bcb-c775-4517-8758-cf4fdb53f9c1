#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أدوات استيراد البيانات من ملفات Excel
Excel Import Utilities
"""

import openpyxl
import sqlite3
from datetime import datetime
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import get_connection

class ExcelImporter:
    def __init__(self):
        self.conn = None
        self.cursor = None
        
    def connect_db(self):
        """الاتصال بقاعدة البيانات"""
        self.conn = get_connection()
        self.cursor = self.conn.cursor()
    
    def close_db(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.conn:
            self.conn.close()
    
    def import_incidents_from_excel(self, file_path, academic_year=None):
        """
        استيراد الحوادث من ملف Excel
        
        Args:
            file_path (str): مسار ملف Excel
            academic_year (str): السنة الدراسية
            
        Returns:
            dict: نتائج الاستيراد
        """
        try:
            self.connect_db()
            
            # فتح ملف Excel
            workbook = openpyxl.load_workbook(file_path)
            
            results = {
                'total_processed': 0,
                'successful_imports': 0,
                'errors': [],
                'sheets_processed': []
            }
            
            # معالجة كل ورقة عمل
            for sheet_name in workbook.sheetnames:
                sheet_result = self._process_sheet(workbook[sheet_name], sheet_name, academic_year)
                results['total_processed'] += sheet_result['processed']
                results['successful_imports'] += sheet_result['successful']
                results['errors'].extend(sheet_result['errors'])
                results['sheets_processed'].append({
                    'name': sheet_name,
                    'processed': sheet_result['processed'],
                    'successful': sheet_result['successful']
                })
            
            self.conn.commit()
            return results
            
        except Exception as e:
            if self.conn:
                self.conn.rollback()
            raise e
        finally:
            self.close_db()
    
    def _process_sheet(self, sheet, sheet_name, academic_year):
        """معالجة ورقة عمل واحدة"""
        result = {
            'processed': 0,
            'successful': 0,
            'errors': []
        }
        
        # تحديد السلك التعليمي من اسم الورقة
        level_mapping = {
            'ابتدائي': 'ابتدائي',
            'إعدادي': 'إعدادي', 
            'تأهيلي': 'تأهيلي',
            'primary': 'ابتدائي',
            'middle': 'إعدادي',
            'secondary': 'تأهيلي'
        }
        
        level = None
        for key, value in level_mapping.items():
            if key.lower() in sheet_name.lower():
                level = value
                break
        
        if not level:
            level = 'ابتدائي'  # افتراضي
        
        # قراءة البيانات من الصف الثاني (تجاهل العناوين)
        for row_num, row in enumerate(sheet.iter_rows(min_row=2, values_only=True), start=2):
            try:
                result['processed'] += 1
                
                # تجاهل الصفوف الفارغة
                if not any(row):
                    continue
                
                # استخراج البيانات
                incident_data = self._extract_incident_data(row, level, academic_year)
                
                if incident_data:
                    # إدراج الحادثة
                    self._insert_incident(incident_data)
                    result['successful'] += 1
                
            except Exception as e:
                error_msg = f"خطأ في الصف {row_num} من ورقة {sheet_name}: {str(e)}"
                result['errors'].append(error_msg)
                print(error_msg)
        
        return result
    
    def _extract_incident_data(self, row, level, academic_year):
        """استخراج بيانات الحادثة من الصف"""
        try:
            # تعيين البيانات حسب ترتيب الأعمدة المحدد
            student_name = str(row[0]).strip() if row[0] else None
            gender = str(row[1]).strip() if row[1] else None
            institution_name = str(row[2]).strip() if row[2] else None
            level_from_excel = str(row[3]).strip() if row[3] else level
            environment = str(row[4]).strip() if row[4] else None
            reference = str(row[5]).strip() if row[5] else None
            incident_date = self._parse_date(row[6])
            settlement_date = self._parse_date(row[7])
            status = str(row[8]).strip() if row[8] else None
            file_number = str(row[9]).strip() if row[9] else None
            
            # التحقق من البيانات المطلوبة
            if not all([student_name, gender, institution_name, incident_date, settlement_date, status]):
                return None
            
            # تنظيف وتوحيد البيانات
            gender = self._normalize_gender(gender)
            environment = self._normalize_environment(environment)
            level_from_excel = self._normalize_level(level_from_excel)
            status = self._normalize_status(status)
            
            return {
                'file_number': file_number,
                'student_name': student_name,
                'gender': gender,
                'institution_name': institution_name,
                'level': level_from_excel or level,
                'environment': environment,
                'reference': reference,
                'incident_date': incident_date,
                'settlement_date': settlement_date,
                'status': status,
                'incident_type': 'حادثة مدرسية',  # افتراضي
                'academic_year': academic_year or '2024-2025'
            }
            
        except Exception as e:
            print(f"خطأ في استخراج البيانات: {e}")
            return None
    
    def _parse_date(self, date_value):
        """تحويل التاريخ إلى تنسيق صحيح"""
        if not date_value:
            return None
        
        try:
            if isinstance(date_value, datetime):
                return date_value.strftime('%Y-%m-%d')
            
            # محاولة تحويل النص إلى تاريخ
            date_str = str(date_value).strip()
            
            # تجربة تنسيقات مختلفة
            formats = ['%Y-%m-%d', '%d/%m/%Y', '%d-%m-%Y', '%Y/%m/%d']
            
            for fmt in formats:
                try:
                    parsed_date = datetime.strptime(date_str, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            return None
            
        except Exception:
            return None
    
    def _normalize_gender(self, gender):
        """توحيد النوع"""
        if not gender:
            return None
        
        gender = gender.lower().strip()
        if gender in ['ذكر', 'male', 'm', 'ذ']:
            return 'ذكر'
        elif gender in ['أنثى', 'female', 'f', 'أ']:
            return 'أنثى'
        
        return None
    
    def _normalize_environment(self, environment):
        """توحيد الوسط"""
        if not environment:
            return 'قروي'  # افتراضي
        
        environment = environment.lower().strip()
        if environment in ['حضري', 'urban', 'city']:
            return 'حضري'
        else:
            return 'قروي'
    
    def _normalize_level(self, level):
        """توحيد السلك التعليمي"""
        if not level:
            return None
        
        level = level.lower().strip()
        if level in ['ابتدائي', 'primary', 'primaire']:
            return 'ابتدائي'
        elif level in ['إعدادي', 'middle', 'college']:
            return 'إعدادي'
        elif level in ['تأهيلي', 'secondary', 'lycee']:
            return 'تأهيلي'
        
        return None
    
    def _normalize_status(self, status):
        """توحيد الحالة"""
        if not status:
            return 'في طور التسوية'  # افتراضي
        
        status = status.lower().strip()
        if 'تسوية' in status and 'تمت' in status:
            return 'تمت التسوية'
        elif 'مرفوض' in status:
            return 'مرفوض'
        elif 'دفع' in status:
            return 'تم الدفع'
        else:
            return 'في طور التسوية'
    
    def _insert_incident(self, data):
        """إدراج الحادثة في قاعدة البيانات"""
        try:
            # التحقق من عدم وجود رقم الملف مسبقاً
            if data.get('file_number'):
                self.cursor.execute('SELECT id FROM incidents WHERE file_number = ?', 
                                  (data['file_number'],))
                if self.cursor.fetchone():
                    print(f"رقم الملف {data['file_number']} موجود مسبقاً")
                    return
            
            # إدراج الحادثة
            self.cursor.execute('''
                INSERT INTO incidents (
                    file_number, student_name, gender, institution_name,
                    level, environment, reference, incident_date,
                    settlement_date, status, incident_type, academic_year
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data.get('file_number'),
                data['student_name'],
                data['gender'],
                data['institution_name'],
                data['level'],
                data['environment'],
                data.get('reference'),
                data['incident_date'],
                data['settlement_date'],
                data['status'],
                data['incident_type'],
                data['academic_year']
            ))
            
        except Exception as e:
            raise Exception(f"خطأ في إدراج الحادثة: {e}")

def import_excel_file(file_path, academic_year=None):
    """
    دالة مساعدة لاستيراد ملف Excel
    
    Args:
        file_path (str): مسار الملف
        academic_year (str): السنة الدراسية
        
    Returns:
        dict: نتائج الاستيراد
    """
    importer = ExcelImporter()
    return importer.import_incidents_from_excel(file_path, academic_year)
