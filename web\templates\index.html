<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة تدبير الحوادث المدرسية - Daman School</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading">
            <div class="spinner"></div>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- حاوي التطبيق -->
    <div class="app-container">
        <!-- الشريط العلوي -->
        <header class="header">
            <div class="header-right">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="app-title">منصة تدبير الحوادث المدرسية</h1>
            </div>
            <div class="header-left">
                <select id="academicYearSelector" class="academic-year-selector">
                    <option value="2024-2025">2024-2025</option>
                </select>
                <button class="btn btn-outline" data-action="refresh">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </header>

        <!-- القائمة الجانبية -->
        <nav class="sidebar">
            <ul class="sidebar-menu">
                <li>
                    <a href="#" data-page="dashboard" class="active">
                        <i class="fas fa-tachometer-alt icon"></i>
                        الرئيسية
                    </a>
                </li>
                <li>
                    <a href="/add_incident" data-page="add_incident">
                        <i class="fas fa-plus-circle icon"></i>
                        إضافة حادثة
                    </a>
                </li>
                <li>
                    <a href="/incidents" data-page="incidents">
                        <i class="fas fa-list icon"></i>
                        قائمة الحوادث
                    </a>
                </li>
                <li>
                    <a href="/institutions" data-page="institutions">
                        <i class="fas fa-school icon"></i>
                        المؤسسات التعليمية
                    </a>
                </li>
                <li>
                    <a href="/reports" data-page="reports">
                        <i class="fas fa-chart-bar icon"></i>
                        التقارير
                    </a>
                </li>
                <li>
                    <a href="/settings" data-page="settings">
                        <i class="fas fa-cog icon"></i>
                        الإعدادات
                    </a>
                </li>
            </ul>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- حاوي التنبيهات -->
            <div id="alertContainer"></div>

            <!-- لوحة التحكم الرئيسية -->
            <div id="dashboardContent">
                <!-- الإحصائيات الرئيسية -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalIncidents">0</div>
                        <div class="stat-label">إجمالي الحوادث</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number" id="pendingIncidents">0</div>
                        <div class="stat-label">في طور التسوية</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number" id="resolvedIncidents">0</div>
                        <div class="stat-label">تمت التسوية</div>
                    </div>
                    <div class="stat-card danger">
                        <div class="stat-number" id="rejectedIncidents">0</div>
                        <div class="stat-label">مرفوض</div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الإجراءات السريعة</h3>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-center" style="gap: 1rem; flex-wrap: wrap;">
                            <button class="btn btn-primary btn-lg" data-action="add-incident">
                                <i class="fas fa-plus-circle"></i>
                                إضافة حادثة جديدة
                            </button>
                            <button class="btn btn-secondary btn-lg" data-action="view-incidents">
                                <i class="fas fa-list"></i>
                                عرض جميع الحوادث
                            </button>
                            <button class="btn btn-success btn-lg" data-action="view-institutions">
                                <i class="fas fa-school"></i>
                                إدارة المؤسسات
                            </button>
                            <button class="btn btn-warning btn-lg" data-action="view-reports">
                                <i class="fas fa-chart-bar"></i>
                                التقارير والإحصائيات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- آخر الحوادث المسجلة -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">آخر الحوادث المسجلة</h3>
                    </div>
                    <div class="card-body">
                        <div id="recentIncidents">
                            <div class="loading">
                                <div class="spinner"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">معلومات النظام</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">السنة الدراسية الحالية:</label>
                                <p id="currentAcademicYear">2024-2025</p>
                            </div>
                            <div class="form-group">
                                <label class="form-label">إصدار التطبيق:</label>
                                <p>1.0.0</p>
                            </div>
                            <div class="form-group">
                                <label class="form-label">آخر تحديث:</label>
                                <p id="lastUpdate">-</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- محتوى الصفحات الأخرى سيتم تحميله ديناميكياً -->
            <div id="dynamicContent" style="display: none;"></div>
        </main>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- إضافة أنماط CSS إضافية للتحسينات -->
    <style>
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            flex-direction: column;
        }

        .loading-overlay p {
            margin-top: 1rem;
            color: var(--primary-color);
            font-weight: 500;
        }

        .incident-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .incident-item:hover {
            background: rgba(52, 152, 219, 0.05);
        }

        .incident-item:last-child {
            border-bottom: none;
        }

        .incident-info h6 {
            margin: 0 0 0.25rem 0;
            color: var(--primary-color);
            font-weight: 600;
        }

        .incident-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .badge-success {
            background: var(--success-color);
            color: white;
        }

        .badge-warning {
            background: var(--warning-color);
            color: white;
        }

        .badge-danger {
            background: var(--danger-color);
            color: white;
        }

        .badge-info {
            background: var(--secondary-color);
            color: white;
        }

        .badge-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: inherit;
            margin-right: 0.5rem;
        }

        .alert {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* تحسينات للتجاوب */
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .d-flex {
                flex-direction: column;
            }
            
            .btn-lg {
                width: 100%;
                margin-bottom: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .incident-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }
    </style>
</body>
</html>
