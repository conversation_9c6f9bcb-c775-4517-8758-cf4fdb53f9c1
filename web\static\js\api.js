/**
 * وحدة API للتفاعل مع الخادم
 * API Module for server communication
 */

class API {
    constructor() {
        this.baseURL = '';
        this.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }

    /**
     * إرسال طلب HTTP
     */
    async request(method, url, data = null) {
        try {
            const config = {
                method: method,
                headers: this.headers
            };

            if (data) {
                config.body = JSON.stringify(data);
            }

            const response = await fetch(this.baseURL + url, config);
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP Error: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }

    // طرق HTTP الأساسية
    async get(url) {
        return this.request('GET', url);
    }

    async post(url, data) {
        return this.request('POST', url, data);
    }

    async put(url, data) {
        return this.request('PUT', url, data);
    }

    async delete(url) {
        return this.request('DELETE', url);
    }

    // === APIs الحوادث ===

    /**
     * الحصول على قائمة الحوادث
     */
    async getIncidents(filters = {}) {
        const params = new URLSearchParams(filters);
        return this.get(`/api/incidents/?${params}`);
    }

    /**
     * الحصول على حادثة محددة
     */
    async getIncident(id) {
        return this.get(`/api/incidents/${id}`);
    }

    /**
     * إنشاء حادثة جديدة
     */
    async createIncident(data) {
        return this.post('/api/incidents/', data);
    }

    /**
     * تحديث حادثة
     */
    async updateIncident(id, data) {
        return this.put(`/api/incidents/${id}`, data);
    }

    /**
     * حذف حادثة
     */
    async deleteIncident(id) {
        return this.delete(`/api/incidents/${id}`);
    }

    // === APIs المؤسسات ===

    /**
     * الحصول على قائمة المؤسسات
     */
    async getInstitutions(filters = {}) {
        const params = new URLSearchParams(filters);
        return this.get(`/api/institutions/?${params}`);
    }

    /**
     * الحصول على مؤسسة محددة
     */
    async getInstitution(id) {
        return this.get(`/api/institutions/${id}`);
    }

    /**
     * إنشاء مؤسسة جديدة
     */
    async createInstitution(data) {
        return this.post('/api/institutions/', data);
    }

    /**
     * تحديث مؤسسة
     */
    async updateInstitution(id, data) {
        return this.put(`/api/institutions/${id}`, data);
    }

    /**
     * حذف مؤسسة
     */
    async deleteInstitution(id) {
        return this.delete(`/api/institutions/${id}`);
    }

    /**
     * البحث في المؤسسات
     */
    async searchInstitutions(query, academicYear = null) {
        const params = new URLSearchParams({ q: query });
        if (academicYear) {
            params.append('academic_year', academicYear);
        }
        return this.get(`/api/institutions/search?${params}`);
    }

    // === APIs التقارير ===

    /**
     * الحصول على إحصائيات لوحة التحكم
     */
    async getDashboardStats(academicYear = null) {
        const params = academicYear ? `?academic_year=${academicYear}` : '';
        return this.get(`/api/reports/dashboard${params}`);
    }

    /**
     * الحصول على ملخص المؤسسات
     */
    async getInstitutionsSummary(academicYear = null) {
        const params = academicYear ? `?academic_year=${academicYear}` : '';
        return this.get(`/api/reports/institutions_summary${params}`);
    }

    /**
     * الحصول على تحليل الحوادث
     */
    async getIncidentsAnalysis(filters = {}) {
        const params = new URLSearchParams(filters);
        return this.get(`/api/reports/incidents_analysis?${params}`);
    }

    /**
     * تصدير التقارير
     */
    async exportReport(type, format, filters = {}) {
        return this.post('/api/reports/export', {
            type: type,
            format: format,
            filters: filters
        });
    }

    // === APIs عامة ===

    /**
     * الحصول على السنوات الدراسية
     */
    async getAcademicYears() {
        return this.get('/api/academic_years');
    }

    /**
     * الحصول على إحصائيات لوحة التحكم الرئيسية
     */
    async getDashboardMainStats() {
        return this.get('/api/dashboard_stats');
    }
}

// إنشاء مثيل عام من API
const api = new API();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = API;
}
