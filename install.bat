@echo off
echo ========================================
echo    تثبيت منصة تدبير الحوادث المدرسية
echo    Installing Daman School System
echo ========================================
echo.

echo تحقق من وجود Python...
echo Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo Error: Python is not installed or not in PATH
    echo.
    echo يرجى تثبيت Python 3.8 أو أحدث من:
    echo Please install Python 3.8 or newer from:
    echo https://www.python.org/downloads/
    pause
    exit /b 1
)
echo.

echo تثبيت المتطلبات...
echo Installing requirements...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo خطأ في تثبيت المتطلبات
    echo Error installing requirements
    pause
    exit /b 1
)
echo.

echo إنشاء البيانات التجريبية...
echo Creating sample data...
python create_sample_data.py
if %errorlevel% neq 0 (
    echo تحذير: لم يتم إنشاء البيانات التجريبية
    echo Warning: Sample data creation failed
)
echo.

echo تم التثبيت بنجاح!
echo Installation completed successfully!
echo.
echo يمكنك الآن تشغيل التطبيق باستخدام:
echo You can now run the application using:
echo   run.bat          (وضع التطوير / Development mode)
echo   run_production.py (وضع الإنتاج / Production mode)
echo.

pause
