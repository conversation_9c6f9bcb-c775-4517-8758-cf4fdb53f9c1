#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق تدبير الحوادث المدرسية
Daman School Incidents Management System
"""

import os
import sys
import webview
from flask import Flask, render_template, request, jsonify, send_from_directory
from datetime import datetime
import sqlite3
import json

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.database import init_database
from api.incidents import incidents_bp
from api.institutions import institutions_bp
from api.reports import reports_bp

# إنشاء تطبيق Flask
app = Flask(__name__, 
           template_folder='web/templates',
           static_folder='web/static')

app.config['SECRET_KEY'] = 'daman_school_secret_key_2024'
app.config['JSON_AS_ASCII'] = False  # لدعم العربية في JSON

# تسجيل Blueprints
app.register_blueprint(incidents_bp, url_prefix='/api/incidents')
app.register_blueprint(institutions_bp, url_prefix='/api/institutions')
app.register_blueprint(reports_bp, url_prefix='/api/reports')

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/add_incident')
def add_incident():
    """صفحة إضافة حادثة"""
    return render_template('add_incident.html')

@app.route('/incidents')
def incidents():
    """صفحة قائمة الحوادث"""
    return render_template('incidents.html')

@app.route('/institutions')
def institutions():
    """صفحة المؤسسات التعليمية"""
    return render_template('institutions.html')

@app.route('/reports')
def reports():
    """صفحة التقارير"""
    return render_template('reports.html')

@app.route('/settings')
def settings():
    """صفحة الإعدادات"""
    return render_template('settings.html')

@app.route('/api/academic_years')
def get_academic_years():
    """الحصول على قائمة السنوات الدراسية"""
    current_year = datetime.now().year
    years = []
    for i in range(2018, current_year + 2):
        years.append(f"{i}-{i+1}")
    return jsonify(years)

@app.route('/api/dashboard_stats')
def dashboard_stats():
    """إحصائيات لوحة التحكم"""
    try:
        # استخدام مسار مباشر لقاعدة البيانات
        db_path = 'database/daman_school.db'

        # التحقق من وجود قاعدة البيانات
        if not os.path.exists(db_path):
            print(f"قاعدة البيانات غير موجودة: {db_path}")
            return jsonify({
                'total_incidents': 0,
                'pending': 0,
                'resolved': 0,
                'rejected': 0,
                'recent_incidents': []
            })

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # إجمالي الحوادث
        cursor.execute("SELECT COUNT(*) FROM incidents")
        result = cursor.fetchone()
        total_incidents = result[0] if result else 0

        # في طور التسوية
        cursor.execute("SELECT COUNT(*) FROM incidents WHERE status = 'في طور التسوية'")
        result = cursor.fetchone()
        pending = result[0] if result else 0

        # تمت التسوية
        cursor.execute("SELECT COUNT(*) FROM incidents WHERE status = 'تمت التسوية'")
        result = cursor.fetchone()
        resolved = result[0] if result else 0

        # مرفوض
        cursor.execute("SELECT COUNT(*) FROM incidents WHERE status = 'مرفوض'")
        result = cursor.fetchone()
        rejected = result[0] if result else 0

        # آخر الحوادث
        cursor.execute("""
            SELECT student_name, institution_name, incident_date, status
            FROM incidents
            ORDER BY created_at DESC
            LIMIT 5
        """)
        recent_incidents = cursor.fetchall()

        conn.close()

        return jsonify({
            'total_incidents': total_incidents,
            'pending': pending,
            'resolved': resolved,
            'rejected': rejected,
            'recent_incidents': recent_incidents
        })
    except Exception as e:
        print(f"خطأ في dashboard_stats: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'total_incidents': 0,
            'pending': 0,
            'resolved': 0,
            'rejected': 0,
            'recent_incidents': []
        })

def create_window():
    """إنشاء نافذة التطبيق"""
    # تهيئة قاعدة البيانات
    init_database()
    
    # إنشاء النافذة
    window = webview.create_window(
        'منصة تدبير الحوادث المدرسية - Daman School',
        app,
        width=1200,
        height=800,
        min_size=(1000, 600),
        resizable=True,
        shadow=True,
        on_top=False
    )
    
    return window

if __name__ == '__main__':
    # تشغيل التطبيق
    if len(sys.argv) > 1 and sys.argv[1] == '--debug':
        # وضع التطوير
        app.run(debug=True, host='127.0.0.1', port=5000)
    else:
        # وضع الإنتاج
        window = create_window()
        webview.start(debug=False)
