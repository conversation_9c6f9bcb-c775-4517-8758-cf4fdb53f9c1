<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المؤسسات التعليمية - منصة تدبير الحوادث المدرسية</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading">
            <div class="spinner"></div>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- حاوي التطبيق -->
    <div class="app-container">
        <!-- الشريط العلوي -->
        <header class="header">
            <div class="header-right">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="app-title">المؤسسات التعليمية</h1>
            </div>
            <div class="header-left">
                <select id="academicYearSelector" class="academic-year-selector">
                    <option value="2024-2025">2024-2025</option>
                </select>
                <a href="/" class="btn btn-outline">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
        </header>

        <!-- القائمة الجانبية -->
        <nav class="sidebar">
            <ul class="sidebar-menu">
                <li>
                    <a href="/" data-page="dashboard">
                        <i class="fas fa-tachometer-alt icon"></i>
                        الرئيسية
                    </a>
                </li>
                <li>
                    <a href="/add_incident" data-page="add_incident">
                        <i class="fas fa-plus-circle icon"></i>
                        إضافة حادثة
                    </a>
                </li>
                <li>
                    <a href="/incidents" data-page="incidents">
                        <i class="fas fa-list icon"></i>
                        قائمة الحوادث
                    </a>
                </li>
                <li>
                    <a href="/institutions" data-page="institutions" class="active">
                        <i class="fas fa-school icon"></i>
                        المؤسسات التعليمية
                    </a>
                </li>
                <li>
                    <a href="/reports" data-page="reports">
                        <i class="fas fa-chart-bar icon"></i>
                        التقارير
                    </a>
                </li>
                <li>
                    <a href="/settings" data-page="settings">
                        <i class="fas fa-cog icon"></i>
                        الإعدادات
                    </a>
                </li>
            </ul>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- حاوي التنبيهات -->
            <div id="alertContainer"></div>

            <!-- قسم إضافة مؤسسة جديدة -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus-circle"></i>
                        إضافة مؤسسة تعليمية جديدة
                    </h3>
                    <button id="toggleFormBtn" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i>
                        إضافة مؤسسة
                    </button>
                </div>
                <div class="card-body" id="institutionFormContainer" style="display: none;">
                    <form id="institutionForm" data-form="institution">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="institutionCode" class="form-label">رمز المؤسسة *</label>
                                <input type="text" id="institutionCode" name="code" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="institutionName" class="form-label">اسم المؤسسة *</label>
                                <input type="text" id="institutionName" name="name" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="commune" class="form-label">اسم الجماعة</label>
                                <input type="text" id="commune" name="commune" class="form-control">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="environment" class="form-label">الوسط *</label>
                                <select id="environment" name="environment" class="form-control" required>
                                    <option value="">اختر الوسط</option>
                                    <option value="قروي">قروي</option>
                                    <option value="حضري">حضري</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="level" class="form-label">السلك *</label>
                                <select id="level" name="level" class="form-control" required>
                                    <option value="">اختر السلك</option>
                                    <option value="ابتدائي">ابتدائي</option>
                                    <option value="إعدادي">إعدادي</option>
                                    <option value="تأهيلي">تأهيلي</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="nature" class="form-label">طبيعة المؤسسة</label>
                                <input type="text" id="nature" name="nature" class="form-control" placeholder="مثال: عمومية، خصوصية">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="directorName" class="form-label">اسم المدير</label>
                                <input type="text" id="directorName" name="director_name" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" id="phone" name="phone" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" id="email" name="email" class="form-control">
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ المؤسسة
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                            <button type="button" id="cancelFormBtn" class="btn btn-outline">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قسم التصفية والبحث -->
            <div class="filters-section">
                <h3>البحث والتصفية</h3>
                <form id="filtersForm">
                    <div class="filters-grid">
                        <div class="form-group">
                            <label class="form-label">البحث العام</label>
                            <div class="search-box">
                                <input type="text" id="searchInput" name="search" class="form-control" placeholder="البحث في اسم المؤسسة، الرمز، أو اسم المدير...">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">السلك</label>
                            <select name="level" class="form-control">
                                <option value="">جميع الأسلاك</option>
                                <option value="ابتدائي">ابتدائي</option>
                                <option value="إعدادي">إعدادي</option>
                                <option value="تأهيلي">تأهيلي</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الجماعة</label>
                            <input type="text" name="commune" class="form-control" placeholder="اسم الجماعة">
                        </div>
                        <div class="form-group">
                            <label class="form-label">الوسط</label>
                            <select name="environment" class="form-control">
                                <option value="">جميع الأوساط</option>
                                <option value="قروي">قروي</option>
                                <option value="حضري">حضري</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">طبيعة المؤسسة</label>
                            <input type="text" name="nature" class="form-control" placeholder="طبيعة المؤسسة">
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i>
                            تطبيق التصفية
                        </button>
                        <button type="reset" class="btn btn-secondary">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                        <button type="button" id="exportInstitutionsBtn" class="btn btn-success">
                            <i class="fas fa-download"></i>
                            تصدير النتائج
                        </button>
                    </div>
                </form>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalInstitutions">0</div>
                    <div class="stat-label">إجمالي المؤسسات</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-number" id="primaryInstitutions">0</div>
                    <div class="stat-label">ابتدائي</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-number" id="middleInstitutions">0</div>
                    <div class="stat-label">إعدادي</div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-number" id="secondaryInstitutions">0</div>
                    <div class="stat-label">تأهيلي</div>
                </div>
            </div>

            <!-- جدول المؤسسات -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-school"></i>
                        قائمة المؤسسات التعليمية
                    </h3>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table" id="institutionsTable">
                            <thead>
                                <tr>
                                    <th>الرمز</th>
                                    <th>اسم المؤسسة</th>
                                    <th>الجماعة</th>
                                    <th>الوسط</th>
                                    <th>السلك</th>
                                    <th>طبيعة المؤسسة</th>
                                    <th>اسم المدير</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="institutionsTableBody">
                                <tr>
                                    <td colspan="8" class="text-center">
                                        <div class="loading">
                                            <div class="spinner"></div>
                                            <p>جاري تحميل البيانات...</p>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- التصفح -->
                    <div id="paginationContainer" class="pagination-container text-center mt-3">
                        <!-- سيتم إضافة أزرار التصفح هنا -->
                    </div>
                </div>
            </div>

            <!-- نافذة تفاصيل المؤسسة -->
            <div id="institutionModal" class="modal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>تفاصيل المؤسسة</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body" id="institutionDetails">
                        <!-- سيتم تحميل التفاصيل هنا -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- JavaScript خاص بصفحة المؤسسات -->
    <script>
        let currentFilters = {};
        let currentPage = 1;
        let totalPages = 1;

        document.addEventListener('DOMContentLoaded', function() {
            initializeInstitutionsPage();
        });

        async function initializeInstitutionsPage() {
            // تحميل البيانات الأولية
            await loadInstitutions();
            
            // تهيئة النموذج
            initializeForm();
            
            // تهيئة أحداث التصفية
            initializeFilters();
            
            // تهيئة أحداث التصدير
            initializeExport();
        }

        function initializeForm() {
            const toggleBtn = document.getElementById('toggleFormBtn');
            const cancelBtn = document.getElementById('cancelFormBtn');
            const formContainer = document.getElementById('institutionFormContainer');
            const form = document.getElementById('institutionForm');
            
            toggleBtn.addEventListener('click', function() {
                formContainer.style.display = formContainer.style.display === 'none' ? 'block' : 'none';
                if (formContainer.style.display === 'block') {
                    toggleBtn.innerHTML = '<i class="fas fa-minus"></i> إخفاء النموذج';
                } else {
                    toggleBtn.innerHTML = '<i class="fas fa-plus"></i> إضافة مؤسسة';
                }
            });
            
            cancelBtn.addEventListener('click', function() {
                formContainer.style.display = 'none';
                toggleBtn.innerHTML = '<i class="fas fa-plus"></i> إضافة مؤسسة';
                form.reset();
            });
        }

        function initializeFilters() {
            const filtersForm = document.getElementById('filtersForm');
            const searchInput = document.getElementById('searchInput');
            
            // تطبيق التصفية عند إرسال النموذج
            filtersForm.addEventListener('submit', function(e) {
                e.preventDefault();
                applyFilters();
            });
            
            // إعادة تعيين التصفية
            filtersForm.addEventListener('reset', function() {
                currentFilters = {};
                currentPage = 1;
                loadInstitutions();
            });
            
            // البحث المباشر
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    applyFilters();
                }, 500);
            });
        }

        function initializeExport() {
            const exportBtn = document.getElementById('exportInstitutionsBtn');
            exportBtn.addEventListener('click', function() {
                exportInstitutions();
            });
        }

        async function applyFilters() {
            const form = document.getElementById('filtersForm');
            const formData = new FormData(form);
            
            currentFilters = {};
            for (let [key, value] of formData.entries()) {
                if (value.trim()) {
                    currentFilters[key] = value.trim();
                }
            }
            
            // إضافة السنة الدراسية
            currentFilters.academic_year = currentAcademicYear;
            
            currentPage = 1;
            await loadInstitutions();
        }

        async function loadInstitutions() {
            try {
                showLoading();
                
                const params = {
                    ...currentFilters,
                    page: currentPage,
                    per_page: 20
                };
                
                const response = await api.getInstitutions(params);
                
                displayInstitutions(response.institutions);
                updatePagination(response);
                updateInstitutionsStats(response.institutions);
                
                hideLoading();
            } catch (error) {
                console.error('خطأ في تحميل المؤسسات:', error);
                showAlert('حدث خطأ في تحميل البيانات', 'danger');
                hideLoading();
            }
        }

        function displayInstitutions(institutions) {
            const tbody = document.getElementById('institutionsTableBody');
            
            if (institutions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center">
                            <p>لا توجد مؤسسات مطابقة للمعايير المحددة</p>
                        </td>
                    </tr>
                `;
                return;
            }
            
            const html = institutions.map(institution => `
                <tr onclick="showInstitutionDetails(${institution.id})" style="cursor: pointer;">
                    <td>${institution.code}</td>
                    <td>${institution.name}</td>
                    <td>${institution.commune || '-'}</td>
                    <td>${institution.environment}</td>
                    <td>${institution.level}</td>
                    <td>${institution.nature || '-'}</td>
                    <td>${institution.director_name || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); showInstitutionDetails(${institution.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="event.stopPropagation(); editInstitution(${institution.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="event.stopPropagation(); deleteInstitution(${institution.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
            
            tbody.innerHTML = html;
        }

        function updatePagination(response) {
            const container = document.getElementById('paginationContainer');
            totalPages = response.pages;
            
            if (totalPages <= 1) {
                container.innerHTML = '';
                return;
            }
            
            let html = '<div class="pagination">';
            
            // زر السابق
            if (currentPage > 1) {
                html += `<button class="btn btn-outline btn-sm" onclick="changePage(${currentPage - 1})">السابق</button>`;
            }
            
            // أرقام الصفحات
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const activeClass = i === currentPage ? 'btn-primary' : 'btn-outline';
                html += `<button class="btn ${activeClass} btn-sm" onclick="changePage(${i})">${i}</button>`;
            }
            
            // زر التالي
            if (currentPage < totalPages) {
                html += `<button class="btn btn-outline btn-sm" onclick="changePage(${currentPage + 1})">التالي</button>`;
            }
            
            html += '</div>';
            html += `<p class="mt-2">الصفحة ${currentPage} من ${totalPages} (إجمالي: ${response.total} مؤسسة)</p>`;
            
            container.innerHTML = html;
        }

        function updateInstitutionsStats(institutions) {
            const stats = {
                total: institutions.length,
                primary: institutions.filter(i => i.level === 'ابتدائي').length,
                middle: institutions.filter(i => i.level === 'إعدادي').length,
                secondary: institutions.filter(i => i.level === 'تأهيلي').length
            };
            
            updateElement('totalInstitutions', stats.total);
            updateElement('primaryInstitutions', stats.primary);
            updateElement('middleInstitutions', stats.middle);
            updateElement('secondaryInstitutions', stats.secondary);
        }

        async function changePage(page) {
            currentPage = page;
            await loadInstitutions();
        }

        async function showInstitutionDetails(institutionId) {
            try {
                const institution = await api.getInstitution(institutionId);
                displayInstitutionModal(institution);
            } catch (error) {
                console.error('خطأ في تحميل تفاصيل المؤسسة:', error);
                showAlert('حدث خطأ في تحميل التفاصيل', 'danger');
            }
        }

        function displayInstitutionModal(institution) {
            const modal = document.getElementById('institutionModal');
            const details = document.getElementById('institutionDetails');
            
            details.innerHTML = `
                <div class="institution-details-grid">
                    <div class="detail-section">
                        <h4>المعلومات الأساسية</h4>
                        <p><strong>رمز المؤسسة:</strong> ${institution.code}</p>
                        <p><strong>اسم المؤسسة:</strong> ${institution.name}</p>
                        <p><strong>اسم الجماعة:</strong> ${institution.commune || 'غير محدد'}</p>
                        <p><strong>الوسط:</strong> ${institution.environment}</p>
                        <p><strong>السلك:</strong> ${institution.level}</p>
                        <p><strong>طبيعة المؤسسة:</strong> ${institution.nature || 'غير محدد'}</p>
                    </div>
                    
                    <div class="detail-section">
                        <h4>معلومات الاتصال</h4>
                        <p><strong>اسم المدير:</strong> ${institution.director_name || 'غير محدد'}</p>
                        <p><strong>رقم الهاتف:</strong> ${institution.phone || 'غير محدد'}</p>
                        <p><strong>البريد الإلكتروني:</strong> ${institution.email || 'غير محدد'}</p>
                    </div>
                    
                    <div class="detail-section">
                        <h4>إحصائيات الحوادث</h4>
                        ${institution.stats ? `
                            <p><strong>إجمالي الحوادث:</strong> ${institution.stats.total_incidents || 0}</p>
                            <p><strong>في طور التسوية:</strong> ${institution.stats.pending || 0}</p>
                            <p><strong>تمت التسوية:</strong> ${institution.stats.resolved || 0}</p>
                            <p><strong>مرفوض:</strong> ${institution.stats.rejected || 0}</p>
                        ` : '<p>لا توجد إحصائيات متاحة</p>'}
                    </div>
                    
                    <div class="detail-section">
                        <h4>معلومات إضافية</h4>
                        <p><strong>السنة الدراسية:</strong> ${institution.academic_year}</p>
                        <p><strong>تاريخ الإنشاء:</strong> ${formatDate(institution.created_at)}</p>
                        <p><strong>آخر تحديث:</strong> ${formatDate(institution.updated_at)}</p>
                    </div>
                </div>
            `;
            
            modal.style.display = 'flex';
        }

        async function editInstitution(institutionId) {
            // TODO: تنفيذ تعديل المؤسسة
            showAlert('ميزة التعديل قيد التطوير', 'info');
        }

        async function deleteInstitution(institutionId) {
            if (!confirmDelete('هل أنت متأكد من حذف هذه المؤسسة؟\nسيتم حذف جميع البيانات المرتبطة بها.')) {
                return;
            }
            
            try {
                await api.deleteInstitution(institutionId);
                showAlert('تم حذف المؤسسة بنجاح', 'success');
                await loadInstitutions();
            } catch (error) {
                console.error('خطأ في حذف المؤسسة:', error);
                showAlert('حدث خطأ في حذف المؤسسة', 'danger');
            }
        }

        async function exportInstitutions() {
            try {
                showLoading();
                
                const result = await api.exportReport('institutions', 'json', currentFilters);
                
                // تحويل البيانات إلى CSV وتنزيلها
                const csvContent = convertToCSV(result.data);
                downloadCSV(csvContent, `institutions_${new Date().toISOString().split('T')[0]}.csv`);
                
                showAlert('تم تصدير البيانات بنجاح', 'success');
                hideLoading();
            } catch (error) {
                console.error('خطأ في تصدير البيانات:', error);
                showAlert('حدث خطأ في تصدير البيانات', 'danger');
                hideLoading();
            }
        }

        function convertToCSV(data) {
            if (!data || data.length === 0) return '';
            
            const headers = Object.keys(data[0]);
            const csvRows = [headers.join(',')];
            
            for (const row of data) {
                const values = headers.map(header => {
                    const value = row[header];
                    return `"${value || ''}"`;
                });
                csvRows.push(values.join(','));
            }
            
            return csvRows.join('\n');
        }

        function downloadCSV(csvContent, filename) {
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // إغلاق النافذة المنبثقة
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('institutionModal');
            if (e.target === modal || e.target.classList.contains('modal-close')) {
                modal.style.display = 'none';
            }
        });
    </script>

    <!-- أنماط CSS إضافية -->
    <style>
        .institution-details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .detail-section h4 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
        }

        .detail-section p {
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .institution-details-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>
