#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات قاعدة البيانات
Database Configuration
"""

import sqlite3
import os
from datetime import datetime

DATABASE_PATH = 'database/daman_school.db'

def get_connection():
    """الحصول على اتصال بقاعدة البيانات"""
    # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
    os.makedirs(os.path.dirname(DATABASE_PATH), exist_ok=True)
    
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
    return conn

def init_database():
    """تهيئة قاعدة البيانات وإنشاء الجداول"""
    conn = get_connection()
    cursor = conn.cursor()
    
    # جدول المؤسسات التعليمية
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS institutions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            commune TEXT,
            environment TEXT NOT NULL CHECK (environment IN ('قروي', 'حضري')),
            level TEXT NOT NULL CHECK (level IN ('ابتدائي', 'إعدادي', 'تأهيلي')),
            nature TEXT,
            director_name TEXT,
            phone TEXT,
            email TEXT,
            academic_year TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول الحوادث
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS incidents (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_number TEXT UNIQUE,
            student_name TEXT NOT NULL,
            student_id TEXT,
            gender TEXT NOT NULL CHECK (gender IN ('ذكر', 'أنثى')),
            institution_name TEXT NOT NULL,
            institution_code TEXT,
            environment TEXT NOT NULL CHECK (environment IN ('قروي', 'حضري')),
            level TEXT NOT NULL CHECK (level IN ('ابتدائي', 'إعدادي', 'تأهيلي')),
            reference TEXT,
            incident_date DATE NOT NULL,
            settlement_date DATE NOT NULL,
            status TEXT NOT NULL CHECK (status IN ('في طور التسوية', 'تمت التسوية', 'مرفوض', 'تم الدفع')),
            incident_type TEXT NOT NULL CHECK (incident_type IN ('حادثة مدرسية', 'حادثة تنقل', 'حادثة رياضية')),
            severity TEXT CHECK (severity IN ('عادية', 'خطيرة', 'وفاة')),
            academic_year TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (institution_code) REFERENCES institutions (code)
        )
    ''')
    
    # جدول المستفيدين من التعويض
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS beneficiaries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            incident_id INTEGER NOT NULL,
            relationship TEXT CHECK (relationship IN ('الأب', 'الأم', 'الولي')),
            name TEXT,
            phone TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (incident_id) REFERENCES incidents (id) ON DELETE CASCADE
        )
    ''')
    
    # جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT NOT NULL CHECK (role IN ('مدير', 'مستخدم')),
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول الإعدادات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT UNIQUE NOT NULL,
            value TEXT,
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إدراج الإعدادات الافتراضية
    default_settings = [
        ('font_size', '14', 'حجم الخط'),
        ('current_academic_year', '2024-2025', 'السنة الدراسية الحالية'),
        ('app_version', '1.0.0', 'إصدار التطبيق'),
        ('backup_path', 'backups/', 'مسار النسخ الاحتياطية')
    ]
    
    for key, value, description in default_settings:
        cursor.execute('''
            INSERT OR IGNORE INTO settings (key, value, description)
            VALUES (?, ?, ?)
        ''', (key, value, description))
    
    # إنشاء مستخدم افتراضي
    cursor.execute('''
        INSERT OR IGNORE INTO users (username, password_hash, role)
        VALUES ('admin', 'admin123', 'مدير')
    ''')
    
    conn.commit()
    conn.close()
    print("تم تهيئة قاعدة البيانات بنجاح")

def execute_query(query, params=None):
    """تنفيذ استعلام قاعدة البيانات"""
    conn = get_connection()
    cursor = conn.cursor()
    
    try:
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if query.strip().upper().startswith('SELECT'):
            result = cursor.fetchall()
        else:
            conn.commit()
            result = cursor.rowcount
        
        return result
    except Exception as e:
        conn.rollback()
        raise e
    finally:
        conn.close()

def get_next_file_number():
    """الحصول على رقم الملف التالي"""
    current_year = datetime.now().year
    conn = get_connection()
    cursor = conn.cursor()
    
    # البحث عن آخر رقم ملف في السنة الحالية
    cursor.execute('''
        SELECT file_number FROM incidents 
        WHERE file_number LIKE ? 
        ORDER BY file_number DESC 
        LIMIT 1
    ''', (f'{current_year}%',))
    
    result = cursor.fetchone()
    conn.close()
    
    if result:
        # استخراج الرقم التسلسلي وزيادته
        last_number = int(result[0].split('-')[1])
        next_number = last_number + 1
    else:
        next_number = 1
    
    return f'{current_year}-{next_number:04d}'
