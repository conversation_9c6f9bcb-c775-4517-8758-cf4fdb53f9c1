@echo off
echo ========================================
echo    بناء تطبيق منصة تدبير الحوادث المدرسية
echo    Building Daman School Application
echo ========================================
echo.

echo تحقق من وجود Python...
echo Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)
echo.

echo تثبيت المتطلبات...
echo Installing requirements...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo خطأ في تثبيت المتطلبات
    echo Error installing requirements
    pause
    exit /b 1
)
echo.

echo بناء التطبيق باستخدام PyInstaller...
echo Building application with PyInstaller...
pyinstaller daman_school.spec
if %errorlevel% neq 0 (
    echo خطأ في بناء التطبيق
    echo Error building application
    pause
    exit /b 1
)
echo.

echo تم بناء التطبيق بنجاح!
echo Application built successfully!
echo الملف التنفيذي موجود في: dist\DamanSchool.exe
echo Executable file location: dist\DamanSchool.exe
echo.

pause
