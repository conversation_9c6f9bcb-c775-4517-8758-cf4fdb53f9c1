#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API للتقارير
Reports API
"""

from flask import Blueprint, request, jsonify
import sqlite3
import sys
import os
from datetime import datetime, timedelta

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import get_connection

reports_bp = Blueprint('reports', __name__)

@reports_bp.route('/dashboard', methods=['GET'])
def dashboard_stats():
    """إحصائيات لوحة التحكم"""
    try:
        academic_year = request.args.get('academic_year', '2024-2025')
        
        conn = get_connection()
        cursor = conn.cursor()
        
        # إجمالي الحوادث
        cursor.execute('SELECT COUNT(*) FROM incidents WHERE academic_year = ?', (academic_year,))
        total_incidents = cursor.fetchone()[0]
        
        # إحصائيات الحالات
        cursor.execute('''
            SELECT status, COUNT(*) 
            FROM incidents 
            WHERE academic_year = ? 
            GROUP BY status
        ''', (academic_year,))
        
        status_stats = {}
        for row in cursor.fetchall():
            status_stats[row[0]] = row[1]
        
        # إحصائيات الأسلاك التعليمية
        cursor.execute('''
            SELECT level, COUNT(*) 
            FROM incidents 
            WHERE academic_year = ? 
            GROUP BY level
        ''', (academic_year,))
        
        level_stats = {}
        for row in cursor.fetchall():
            level_stats[row[0]] = row[1]
        
        # إحصائيات نوع الحادثة
        cursor.execute('''
            SELECT incident_type, COUNT(*) 
            FROM incidents 
            WHERE academic_year = ? 
            GROUP BY incident_type
        ''', (academic_year,))
        
        type_stats = {}
        for row in cursor.fetchall():
            type_stats[row[0]] = row[1]
        
        # آخر الحوادث
        cursor.execute('''
            SELECT student_name, institution_name, incident_date, status, file_number
            FROM incidents 
            WHERE academic_year = ?
            ORDER BY created_at DESC 
            LIMIT 5
        ''', (academic_year,))
        
        recent_incidents = [dict(zip([col[0] for col in cursor.description], row)) 
                           for row in cursor.fetchall()]
        
        # إحصائيات شهرية
        cursor.execute('''
            SELECT strftime('%Y-%m', incident_date) as month, COUNT(*) 
            FROM incidents 
            WHERE academic_year = ?
            GROUP BY strftime('%Y-%m', incident_date)
            ORDER BY month
        ''', (academic_year,))
        
        monthly_stats = {}
        for row in cursor.fetchall():
            monthly_stats[row[0]] = row[1]
        
        conn.close()
        
        return jsonify({
            'total_incidents': total_incidents,
            'status_stats': status_stats,
            'level_stats': level_stats,
            'type_stats': type_stats,
            'recent_incidents': recent_incidents,
            'monthly_stats': monthly_stats
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/institutions_summary', methods=['GET'])
def institutions_summary():
    """ملخص المؤسسات"""
    try:
        academic_year = request.args.get('academic_year', '2024-2025')
        
        conn = get_connection()
        cursor = conn.cursor()
        
        # إحصائيات المؤسسات حسب السلك
        cursor.execute('''
            SELECT level, COUNT(*) 
            FROM institutions 
            WHERE academic_year = ?
            GROUP BY level
        ''', (academic_year,))
        
        level_stats = {}
        for row in cursor.fetchall():
            level_stats[row[0]] = row[1]
        
        # إحصائيات المؤسسات حسب الوسط
        cursor.execute('''
            SELECT environment, COUNT(*) 
            FROM institutions 
            WHERE academic_year = ?
            GROUP BY environment
        ''', (academic_year,))
        
        environment_stats = {}
        for row in cursor.fetchall():
            environment_stats[row[0]] = row[1]
        
        # المؤسسات الأكثر حوادث
        cursor.execute('''
            SELECT i.institution_name, i.institution_code, COUNT(*) as incidents_count
            FROM incidents i
            WHERE i.academic_year = ?
            GROUP BY i.institution_name, i.institution_code
            ORDER BY incidents_count DESC
            LIMIT 10
        ''', (academic_year,))
        
        top_institutions = [dict(zip([col[0] for col in cursor.description], row)) 
                           for row in cursor.fetchall()]
        
        conn.close()
        
        return jsonify({
            'level_stats': level_stats,
            'environment_stats': environment_stats,
            'top_institutions': top_institutions
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/incidents_analysis', methods=['GET'])
def incidents_analysis():
    """تحليل الحوادث"""
    try:
        academic_year = request.args.get('academic_year', '2024-2025')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        conn = get_connection()
        cursor = conn.cursor()
        
        # بناء شروط التصفية
        where_conditions = ['academic_year = ?']
        params = [academic_year]
        
        if date_from:
            where_conditions.append('incident_date >= ?')
            params.append(date_from)
        
        if date_to:
            where_conditions.append('incident_date <= ?')
            params.append(date_to)
        
        where_clause = ' AND '.join(where_conditions)
        
        # تحليل حسب النوع والخطورة
        cursor.execute(f'''
            SELECT incident_type, severity, COUNT(*) 
            FROM incidents 
            WHERE {where_clause}
            GROUP BY incident_type, severity
        ''', params)
        
        type_severity_analysis = []
        for row in cursor.fetchall():
            type_severity_analysis.append({
                'incident_type': row[0],
                'severity': row[1],
                'count': row[2]
            })
        
        # تحليل حسب النوع والوسط
        cursor.execute(f'''
            SELECT gender, environment, COUNT(*) 
            FROM incidents 
            WHERE {where_clause}
            GROUP BY gender, environment
        ''', params)
        
        gender_environment_analysis = []
        for row in cursor.fetchall():
            gender_environment_analysis.append({
                'gender': row[0],
                'environment': row[1],
                'count': row[2]
            })
        
        # تحليل زمني (أسبوعي)
        cursor.execute(f'''
            SELECT strftime('%Y-%W', incident_date) as week, COUNT(*) 
            FROM incidents 
            WHERE {where_clause}
            GROUP BY strftime('%Y-%W', incident_date)
            ORDER BY week
        ''', params)
        
        weekly_analysis = {}
        for row in cursor.fetchall():
            weekly_analysis[row[0]] = row[1]
        
        # متوسط وقت التسوية
        cursor.execute(f'''
            SELECT AVG(julianday(settlement_date) - julianday(incident_date)) as avg_settlement_days
            FROM incidents 
            WHERE {where_clause} AND status = 'تمت التسوية'
        ''', params)
        
        avg_settlement_days = cursor.fetchone()[0] or 0
        
        conn.close()
        
        return jsonify({
            'type_severity_analysis': type_severity_analysis,
            'gender_environment_analysis': gender_environment_analysis,
            'weekly_analysis': weekly_analysis,
            'avg_settlement_days': round(avg_settlement_days, 2)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/export', methods=['POST'])
def export_report():
    """تصدير التقارير"""
    try:
        data = request.get_json()
        report_type = data.get('type')
        format_type = data.get('format', 'json')
        filters = data.get('filters', {})
        
        if report_type == 'incidents':
            return export_incidents_report(filters, format_type)
        elif report_type == 'institutions':
            return export_institutions_report(filters, format_type)
        else:
            return jsonify({'error': 'نوع التقرير غير مدعوم'}), 400
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def export_incidents_report(filters, format_type):
    """تصدير تقرير الحوادث"""
    conn = get_connection()
    cursor = conn.cursor()
    
    # بناء الاستعلام
    query = '''
        SELECT i.file_number, i.student_name, i.gender, i.institution_name,
               i.level, i.environment, i.incident_date, i.settlement_date,
               i.status, i.incident_type, i.severity,
               GROUP_CONCAT(b.name || ' (' || b.relationship || ')') as beneficiaries
        FROM incidents i
        LEFT JOIN beneficiaries b ON i.id = b.incident_id
        WHERE 1=1
    '''
    params = []
    
    # تطبيق المرشحات
    if filters.get('academic_year'):
        query += ' AND i.academic_year = ?'
        params.append(filters['academic_year'])
    
    if filters.get('date_from'):
        query += ' AND i.incident_date >= ?'
        params.append(filters['date_from'])
    
    if filters.get('date_to'):
        query += ' AND i.incident_date <= ?'
        params.append(filters['date_to'])
    
    query += ' GROUP BY i.id ORDER BY i.incident_date DESC'
    
    cursor.execute(query, params)
    incidents = [dict(zip([col[0] for col in cursor.description], row)) 
                for row in cursor.fetchall()]
    
    conn.close()
    
    if format_type == 'json':
        return jsonify({
            'data': incidents,
            'total': len(incidents),
            'exported_at': datetime.now().isoformat()
        })
    else:
        return jsonify({'error': 'تنسيق التصدير غير مدعوم حالياً'}), 400

def export_institutions_report(filters, format_type):
    """تصدير تقرير المؤسسات"""
    conn = get_connection()
    cursor = conn.cursor()
    
    query = 'SELECT * FROM institutions WHERE 1=1'
    params = []
    
    if filters.get('academic_year'):
        query += ' AND academic_year = ?'
        params.append(filters['academic_year'])
    
    query += ' ORDER BY name'
    
    cursor.execute(query, params)
    institutions = [dict(row) for row in cursor.fetchall()]
    
    conn.close()
    
    if format_type == 'json':
        return jsonify({
            'data': institutions,
            'total': len(institutions),
            'exported_at': datetime.now().isoformat()
        })
    else:
        return jsonify({'error': 'تنسيق التصدير غير مدعوم حالياً'}), 400
