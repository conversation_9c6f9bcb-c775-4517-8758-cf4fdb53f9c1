<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - منصة تدبير الحوادث المدرسية</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading">
            <div class="spinner"></div>
            <p>جاري تحميل التقارير...</p>
        </div>
    </div>

    <!-- حاوي التطبيق -->
    <div class="app-container">
        <!-- الشريط العلوي -->
        <header class="header">
            <div class="header-right">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="app-title">التقارير والإحصائيات</h1>
            </div>
            <div class="header-left">
                <select id="academicYearSelector" class="academic-year-selector">
                    <option value="2024-2025">2024-2025</option>
                </select>
                <a href="/" class="btn btn-outline">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
        </header>

        <!-- القائمة الجانبية -->
        <nav class="sidebar">
            <ul class="sidebar-menu">
                <li>
                    <a href="/" data-page="dashboard">
                        <i class="fas fa-tachometer-alt icon"></i>
                        الرئيسية
                    </a>
                </li>
                <li>
                    <a href="/add_incident" data-page="add_incident">
                        <i class="fas fa-plus-circle icon"></i>
                        إضافة حادثة
                    </a>
                </li>
                <li>
                    <a href="/incidents" data-page="incidents">
                        <i class="fas fa-list icon"></i>
                        قائمة الحوادث
                    </a>
                </li>
                <li>
                    <a href="/institutions" data-page="institutions">
                        <i class="fas fa-school icon"></i>
                        المؤسسات التعليمية
                    </a>
                </li>
                <li>
                    <a href="/reports" data-page="reports" class="active">
                        <i class="fas fa-chart-bar icon"></i>
                        التقارير
                    </a>
                </li>
                <li>
                    <a href="/settings" data-page="settings">
                        <i class="fas fa-cog icon"></i>
                        الإعدادات
                    </a>
                </li>
            </ul>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- حاوي التنبيهات -->
            <div id="alertContainer"></div>

            <!-- إحصائيات عامة -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalIncidentsReport">0</div>
                    <div class="stat-label">إجمالي الحوادث</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-number" id="totalInstitutionsReport">0</div>
                    <div class="stat-label">إجمالي المؤسسات</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-number" id="avgSettlementDays">0</div>
                    <div class="stat-label">متوسط أيام التسوية</div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-number" id="criticalIncidents">0</div>
                    <div class="stat-label">الحوادث الخطيرة</div>
                </div>
            </div>

            <!-- مرشحات التقارير -->
            <div class="filters-section">
                <h3>مرشحات التقارير</h3>
                <form id="reportFiltersForm">
                    <div class="filters-grid">
                        <div class="form-group">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" name="date_from" class="form-control" id="reportDateFrom">
                        </div>
                        <div class="form-group">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" name="date_to" class="form-control" id="reportDateTo">
                        </div>
                        <div class="form-group">
                            <label class="form-label">السلك</label>
                            <select name="level" class="form-control">
                                <option value="">جميع الأسلاك</option>
                                <option value="ابتدائي">ابتدائي</option>
                                <option value="إعدادي">إعدادي</option>
                                <option value="تأهيلي">تأهيلي</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الوسط</label>
                            <select name="environment" class="form-control">
                                <option value="">جميع الأوساط</option>
                                <option value="قروي">قروي</option>
                                <option value="حضري">حضري</option>
                            </select>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-chart-line"></i>
                            تحديث التقارير
                        </button>
                        <button type="reset" class="btn btn-secondary">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                        <button type="button" id="exportReportBtn" class="btn btn-success">
                            <i class="fas fa-download"></i>
                            تصدير التقرير
                        </button>
                    </div>
                </form>
            </div>

            <!-- الرسوم البيانية -->
            <div class="charts-grid">
                <!-- رسم بياني للحالات -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-pie-chart"></i>
                            توزيع الحوادث حسب الحالة
                        </h3>
                    </div>
                    <div class="card-body">
                        <canvas id="statusChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- رسم بياني للأسلاك -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bar-chart"></i>
                            توزيع الحوادث حسب السلك التعليمي
                        </h3>
                    </div>
                    <div class="card-body">
                        <canvas id="levelChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- رسم بياني شهري -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-line-chart"></i>
                            تطور الحوادث الشهري
                        </h3>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- رسم بياني لأنواع الحوادث -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-doughnut-chart"></i>
                            توزيع أنواع الحوادث
                        </h3>
                    </div>
                    <div class="card-body">
                        <canvas id="typeChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- جداول التقارير -->
            <div class="reports-tables">
                <!-- أكثر المؤسسات حوادث -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-trophy"></i>
                            المؤسسات الأكثر حوادث
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>المؤسسة</th>
                                        <th>السلك</th>
                                        <th>عدد الحوادث</th>
                                        <th>في طور التسوية</th>
                                        <th>تمت التسوية</th>
                                    </tr>
                                </thead>
                                <tbody id="topInstitutionsTableBody">
                                    <tr>
                                        <td colspan="5" class="text-center">
                                            <div class="loading">
                                                <div class="spinner"></div>
                                                <p>جاري تحميل البيانات...</p>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- تحليل الخطورة -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-exclamation-triangle"></i>
                            تحليل خطورة الحوادث
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="severity-analysis">
                            <div class="severity-item">
                                <div class="severity-label">عادية</div>
                                <div class="severity-bar">
                                    <div class="severity-fill normal" id="normalSeverityBar"></div>
                                </div>
                                <div class="severity-count" id="normalSeverityCount">0</div>
                            </div>
                            <div class="severity-item">
                                <div class="severity-label">خطيرة</div>
                                <div class="severity-bar">
                                    <div class="severity-fill serious" id="seriousSeverityBar"></div>
                                </div>
                                <div class="severity-count" id="seriousSeverityCount">0</div>
                            </div>
                            <div class="severity-item">
                                <div class="severity-label">وفاة</div>
                                <div class="severity-bar">
                                    <div class="severity-fill fatal" id="fatalSeverityBar"></div>
                                </div>
                                <div class="severity-count" id="fatalSeverityCount">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- JavaScript خاص بصفحة التقارير -->
    <script>
        let currentFilters = {};
        let charts = {};

        document.addEventListener('DOMContentLoaded', function() {
            initializeReportsPage();
        });

        async function initializeReportsPage() {
            // تعيين التواريخ الافتراضية
            setDefaultDates();
            
            // تحميل التقارير الأولية
            await loadReports();
            
            // تهيئة أحداث المرشحات
            initializeFilters();
            
            // تهيئة التصدير
            initializeExport();
        }

        function setDefaultDates() {
            const today = new Date();
            const sixMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 6, today.getDate());
            
            document.getElementById('reportDateFrom').value = sixMonthsAgo.toISOString().split('T')[0];
            document.getElementById('reportDateTo').value = today.toISOString().split('T')[0];
        }

        function initializeFilters() {
            const filtersForm = document.getElementById('reportFiltersForm');
            
            filtersForm.addEventListener('submit', function(e) {
                e.preventDefault();
                applyFilters();
            });
            
            filtersForm.addEventListener('reset', function() {
                currentFilters = {};
                setDefaultDates();
                loadReports();
            });
        }

        function initializeExport() {
            const exportBtn = document.getElementById('exportReportBtn');
            exportBtn.addEventListener('click', function() {
                exportReport();
            });
        }

        async function applyFilters() {
            const form = document.getElementById('reportFiltersForm');
            const formData = new FormData(form);
            
            currentFilters = {};
            for (let [key, value] of formData.entries()) {
                if (value.trim()) {
                    currentFilters[key] = value.trim();
                }
            }
            
            currentFilters.academic_year = currentAcademicYear;
            
            await loadReports();
        }

        async function loadReports() {
            try {
                showLoading();
                
                // تحميل الإحصائيات العامة
                const dashboardStats = await api.getDashboardStats(currentAcademicYear);
                updateGeneralStats(dashboardStats);
                
                // تحميل تحليل الحوادث
                const analysisData = await api.getIncidentsAnalysis(currentFilters);
                updateCharts(analysisData);
                updateSeverityAnalysis(analysisData);
                
                // تحميل ملخص المؤسسات
                const institutionsData = await api.getInstitutionsSummary(currentAcademicYear);
                updateTopInstitutions(institutionsData);
                
                hideLoading();
            } catch (error) {
                console.error('خطأ في تحميل التقارير:', error);
                showAlert('حدث خطأ في تحميل التقارير', 'danger');
                hideLoading();
            }
        }

        function updateGeneralStats(stats) {
            updateElement('totalIncidentsReport', stats.total_incidents || 0);
            updateElement('avgSettlementDays', stats.avg_settlement_days || 0);
            
            // حساب الحوادث الخطيرة
            const criticalCount = (stats.type_stats && stats.type_stats['خطيرة']) || 0;
            updateElement('criticalIncidents', criticalCount);
        }

        function updateCharts(data) {
            // رسم بياني للحالات
            updateStatusChart(data.status_stats || {});
            
            // رسم بياني للأسلاك
            updateLevelChart(data.level_stats || {});
            
            // رسم بياني شهري
            updateMonthlyChart(data.monthly_stats || {});
            
            // رسم بياني لأنواع الحوادث
            updateTypeChart(data.type_stats || {});
        }

        function updateStatusChart(statusStats) {
            const ctx = document.getElementById('statusChart').getContext('2d');
            
            if (charts.statusChart) {
                charts.statusChart.destroy();
            }
            
            charts.statusChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: Object.keys(statusStats),
                    datasets: [{
                        data: Object.values(statusStats),
                        backgroundColor: [
                            '#f39c12', // في طور التسوية
                            '#27ae60', // تمت التسوية
                            '#e74c3c', // مرفوض
                            '#3498db'  // تم الدفع
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateLevelChart(levelStats) {
            const ctx = document.getElementById('levelChart').getContext('2d');
            
            if (charts.levelChart) {
                charts.levelChart.destroy();
            }
            
            charts.levelChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Object.keys(levelStats),
                    datasets: [{
                        label: 'عدد الحوادث',
                        data: Object.values(levelStats),
                        backgroundColor: '#3498db'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function updateMonthlyChart(monthlyStats) {
            const ctx = document.getElementById('monthlyChart').getContext('2d');
            
            if (charts.monthlyChart) {
                charts.monthlyChart.destroy();
            }
            
            charts.monthlyChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Object.keys(monthlyStats),
                    datasets: [{
                        label: 'عدد الحوادث',
                        data: Object.values(monthlyStats),
                        borderColor: '#27ae60',
                        backgroundColor: 'rgba(39, 174, 96, 0.1)',
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function updateTypeChart(typeStats) {
            const ctx = document.getElementById('typeChart').getContext('2d');
            
            if (charts.typeChart) {
                charts.typeChart.destroy();
            }
            
            charts.typeChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(typeStats),
                    datasets: [{
                        data: Object.values(typeStats),
                        backgroundColor: [
                            '#e74c3c', // حادثة مدرسية
                            '#f39c12', // حادثة تنقل
                            '#9b59b6'  // حادثة رياضية
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateSeverityAnalysis(data) {
            // محاكاة بيانات الخطورة
            const severityData = {
                'عادية': 30,
                'خطيرة': 15,
                'وفاة': 5
            };
            
            const total = Object.values(severityData).reduce((a, b) => a + b, 0);
            
            Object.keys(severityData).forEach(severity => {
                const count = severityData[severity];
                const percentage = total > 0 ? (count / total) * 100 : 0;
                
                const countElement = document.getElementById(`${getSeverityId(severity)}SeverityCount`);
                const barElement = document.getElementById(`${getSeverityId(severity)}SeverityBar`);
                
                if (countElement) countElement.textContent = count;
                if (barElement) barElement.style.width = percentage + '%';
            });
        }

        function getSeverityId(severity) {
            const mapping = {
                'عادية': 'normal',
                'خطيرة': 'serious',
                'وفاة': 'fatal'
            };
            return mapping[severity] || 'normal';
        }

        function updateTopInstitutions(data) {
            const tbody = document.getElementById('topInstitutionsTableBody');
            
            // محاكاة بيانات المؤسسات
            const institutions = [
                { name: 'مدرسة النور الابتدائية', level: 'ابتدائي', total: 12, pending: 3, resolved: 9 },
                { name: 'ثانوية ابن خلدون الإعدادية', level: 'إعدادي', total: 8, pending: 2, resolved: 6 },
                { name: 'الثانوية التأهيلية محمد الخامس', level: 'تأهيلي', total: 15, pending: 5, resolved: 10 }
            ];
            
            const html = institutions.map(inst => `
                <tr>
                    <td>${inst.name}</td>
                    <td>${inst.level}</td>
                    <td>${inst.total}</td>
                    <td>${inst.pending}</td>
                    <td>${inst.resolved}</td>
                </tr>
            `).join('');
            
            tbody.innerHTML = html;
        }

        async function exportReport() {
            try {
                showLoading();
                
                // محاكاة تصدير التقرير
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                showAlert('تم تصدير التقرير بنجاح', 'success');
                hideLoading();
            } catch (error) {
                console.error('خطأ في تصدير التقرير:', error);
                showAlert('حدث خطأ في تصدير التقرير', 'danger');
                hideLoading();
            }
        }
    </script>

    <!-- أنماط CSS إضافية -->
    <style>
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .reports-tables {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 1.5rem;
        }

        .severity-analysis {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .severity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .severity-label {
            min-width: 80px;
            font-weight: 500;
        }

        .severity-bar {
            flex: 1;
            height: 20px;
            background: var(--light-color);
            border-radius: 10px;
            overflow: hidden;
        }

        .severity-fill {
            height: 100%;
            transition: width 0.3s ease;
        }

        .severity-fill.normal {
            background: var(--success-color);
        }

        .severity-fill.serious {
            background: var(--warning-color);
        }

        .severity-fill.fatal {
            background: var(--danger-color);
        }

        .severity-count {
            min-width: 40px;
            text-align: center;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .reports-tables {
                grid-template-columns: 1fr;
            }
            
            .severity-item {
                flex-direction: column;
                align-items: stretch;
                gap: 0.5rem;
            }
        }
    </style>
</body>
</html>
